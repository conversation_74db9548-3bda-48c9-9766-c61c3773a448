import React, { useState, useEffect, useRef } from 'react';
import { Send, Paperclip, Plus, Download, Trash2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Thum<PERSON>U<PERSON>, <PERSON><PERSON>, User, X } from 'lucide-react';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
}

export interface ChatFile {
  name: string;
  type: string;
  data: string; // base64 encoded
}

export interface ChatRequest {
  model: string;
  provider: string;
  messages: ChatMessage[];
  files?: ChatFile[];
}

interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  model: string;
  provider: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ChatbotProps {
  className?: string;
}

const Chatbot: React.FC<ChatbotProps> = ({ className = '' }) => {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<ChatFile[]>([]);
  const [selectedModel, setSelectedModel] = useState('gpt-4');
  const [selectedProvider, setSelectedProvider] = useState('auto');
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [availableProviders, setAvailableProviders] = useState<string[]>([]);
  const [isServiceConfigured, setIsServiceConfigured] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    initializeChatbot();
    loadConversations();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  const initializeChatbot = async () => {
    if (!window.electronAPI) {
      console.error('Electron API not available');
      return;
    }

    try {
      // Check if service is configured
      const configResult = await window.electronAPI.gpt4freeChatIsConfigured();
      setIsServiceConfigured(configResult.configured || false);

      // Get available models
      const modelsResult = await window.electronAPI.gpt4freeChatGetModels();
      if (modelsResult.success && modelsResult.models) {
        setAvailableModels(modelsResult.models);
      } else {
        // Set default models if service not configured
        setAvailableModels([
          'gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet',
          'gemini-pro', 'llama-2-70b', 'mixtral-8x7b', 'deepseek-coder'
        ]);
      }

      // Get available providers
      const providersResult = await window.electronAPI.gpt4freeChatGetProviders();
      if (providersResult.success && providersResult.providers) {
        setAvailableProviders(providersResult.providers);
      } else {
        setAvailableProviders(['auto', 'bing', 'you', 'phind', 'liaobots', 'g4f']);
      }
    } catch (error) {
      console.error('Failed to initialize chatbot:', error);
      // Set defaults on error
      setIsServiceConfigured(false);
      setAvailableModels(['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus']);
      setAvailableProviders(['auto', 'bing', 'you', 'phind']);
    }
  };

  const loadConversations = () => {
    const saved = localStorage.getItem('gpt4free_conversations');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        const conversationsWithDates = parsed.map((conv: any) => ({
          ...conv,
          createdAt: new Date(conv.createdAt),
          updatedAt: new Date(conv.updatedAt)
        }));
        setConversations(conversationsWithDates);
      } catch (error) {
        console.error('Error loading conversations:', error);
      }
    }
  };

  const saveConversations = (updatedConversations: Conversation[]) => {
    localStorage.setItem('gpt4free_conversations', JSON.stringify(updatedConversations));
    setConversations(updatedConversations);
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const createNewConversation = () => {
    const newConversation: Conversation = {
      id: Date.now().toString(),
      title: 'New Conversation',
      messages: [{
        role: 'assistant',
        content: 'Hello! I\'m your GPT4Free AI assistant. How can I help you today?'
      }],
      model: selectedModel,
      provider: selectedProvider,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const updatedConversations = [newConversation, ...conversations];
    saveConversations(updatedConversations);
    setCurrentConversationId(newConversation.id);
    setMessages(newConversation.messages);
  };

  const switchToConversation = (conversationId: string) => {
    const conversation = conversations.find(conv => conv.id === conversationId);
    if (conversation) {
      setCurrentConversationId(conversationId);
      setMessages(conversation.messages);
      setSelectedModel(conversation.model);
      setSelectedProvider(conversation.provider);
    }
  };

  const updateCurrentConversation = () => {
    if (!currentConversationId) return;

    const updatedConversations = conversations.map(conv => {
      if (conv.id === currentConversationId) {
        const updatedConv = {
          ...conv,
          messages: [...messages],
          model: selectedModel,
          provider: selectedProvider,
          updatedAt: new Date()
        };

        // Update title if it's still "New Conversation"
        if (conv.title === 'New Conversation' && messages.length > 1) {
          const firstUserMessage = messages.find(msg => msg.role === 'user');
          if (firstUserMessage) {
            updatedConv.title = firstUserMessage.content.substring(0, 50) + 
              (firstUserMessage.content.length > 50 ? '...' : '');
          }
        }

        return updatedConv;
      }
      return conv;
    });

    saveConversations(updatedConversations);
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() && attachedFiles.length === 0) return;

    const userMessage: ChatMessage = {
      role: 'user',
      content: inputMessage.trim()
    };

    const newMessages = [...messages, userMessage];
    setMessages(newMessages);
    setInputMessage('');
    setIsTyping(true);

    try {
      if (!isServiceConfigured) {
        // Mock response for when service is not configured
        setTimeout(() => {
          const mockResponse: ChatMessage = {
            role: 'assistant',
            content: `I understand your message: "${userMessage.content}". However, the GPT4Free service is not currently configured. Please ensure Python and the g4f library are installed to get real AI responses.`
          };
          setMessages(prev => [...prev, mockResponse]);
          setIsTyping(false);
          updateCurrentConversation();
        }, 1000);
        return;
      }

      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      const result = await window.electronAPI.gpt4freeChatGenerate({
        model: selectedModel,
        provider: selectedProvider,
        messages: newMessages,
        files: attachedFiles
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to generate response');
      }

      const assistantMessage: ChatMessage = {
        role: 'assistant',
        content: result.response?.response || 'No response received'
      };

      setMessages(prev => [...prev, assistantMessage]);
      setAttachedFiles([]);
      
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: ChatMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error while processing your request. Please try again.'
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
      updateCurrentConversation();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    files.forEach(file => {
      if (file.size > 10 * 1024 * 1024) {
        alert(`File ${file.name} is too large. Maximum size is 10MB.`);
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const chatFile: ChatFile = {
          name: file.name,
          type: file.type,
          data: e.target?.result as string
        };
        setAttachedFiles(prev => [...prev, chatFile]);
      };
      reader.readAsDataURL(file);
    });

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const removeFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const clearCurrentChat = () => {
    if (window.confirm('Are you sure you want to clear this conversation?')) {
      const welcomeMessage: ChatMessage = {
        role: 'assistant',
        content: 'Hello! I\'m your GPT4Free AI assistant. How can I help you today?'
      };
      setMessages([welcomeMessage]);
      updateCurrentConversation();
    }
  };

  const exportChat = () => {
    if (!currentConversationId) return;
    
    const conversation = conversations.find(conv => conv.id === currentConversationId);
    if (!conversation) return;

    const exportData = {
      title: conversation.title,
      model: conversation.model,
      provider: conversation.provider,
      createdAt: conversation.createdAt,
      messages: conversation.messages
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chat-${conversation.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Initialize with first conversation if none exists
  useEffect(() => {
    if (conversations.length === 0) {
      createNewConversation();
    } else if (!currentConversationId && conversations.length > 0) {
      switchToConversation(conversations[0].id);
    }
  }, [conversations.length, currentConversationId]);

  return (
    <div className={`flex h-full bg-gray-900 text-white ${className}`}>
      {/* Sidebar */}
      <div className="w-80 bg-gray-800 border-r border-gray-700 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <h1 className="text-xl font-bold flex items-center gap-2">
            <Bot className="w-6 h-6 text-blue-400" />
            GPT4Free Chat
          </h1>
          <p className="text-sm text-gray-400 mt-1">Unlimited AI conversations</p>
        </div>

        {/* Model Selection */}
        <div className="p-4 border-b border-gray-700">
          <h3 className="text-sm font-medium text-blue-400 mb-3">AI Model</h3>
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white text-sm mb-2"
          >
            {availableModels.map(model => (
              <option key={model} value={model}>
                {model.toUpperCase()}
              </option>
            ))}
          </select>
          <select
            value={selectedProvider}
            onChange={(e) => setSelectedProvider(e.target.value)}
            className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white text-xs"
          >
            {availableProviders.map(provider => (
              <option key={provider} value={provider}>
                {provider === 'auto' ? 'Auto Select Provider' : provider.charAt(0).toUpperCase() + provider.slice(1)}
              </option>
            ))}
          </select>
        </div>

        {/* Conversations */}
        <div className="flex-1 p-4 overflow-y-auto">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-blue-400">Conversations</h3>
            <button
              onClick={createNewConversation}
              className="p-1 hover:bg-gray-700 rounded"
              title="New Chat"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
          
          <button
            onClick={createNewConversation}
            className="w-full p-3 bg-blue-600 hover:bg-blue-700 rounded-lg mb-3 flex items-center gap-2 text-sm font-medium transition-colors"
          >
            <Plus className="w-4 h-4" />
            New Chat
          </button>

          <div className="space-y-2">
            {conversations.map(conv => (
              <div
                key={conv.id}
                onClick={() => switchToConversation(conv.id)}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  conv.id === currentConversationId
                    ? 'bg-blue-600/20 border border-blue-500/30'
                    : 'bg-gray-700/50 hover:bg-gray-700'
                }`}
              >
                <div className="font-medium text-sm truncate">{conv.title}</div>
                <div className="text-xs text-gray-400 mt-1">
                  {conv.messages.length > 1 
                    ? conv.messages[conv.messages.length - 1].content.substring(0, 40) + '...'
                    : 'No messages yet'
                  }
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-gray-700 flex items-center justify-between bg-gray-800">
          <div>
            <h2 className="font-semibold">
              {conversations.find(conv => conv.id === currentConversationId)?.title || 'New Conversation'}
            </h2>
            <p className="text-xs text-gray-400">
              {selectedModel} • {selectedProvider} • {isServiceConfigured ? 'Connected' : 'Demo Mode'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={clearCurrentChat}
              className="p-2 hover:bg-gray-700 rounded"
              title="Clear Chat"
            >
              <Trash2 className="w-4 h-4" />
            </button>
            <button
              onClick={exportChat}
              className="p-2 hover:bg-gray-700 rounded"
              title="Export Chat"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`flex items-start gap-3 ${
                message.role === 'user' ? 'flex-row-reverse' : ''
              }`}
            >
              <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                message.role === 'user' 
                  ? 'bg-blue-600' 
                  : 'bg-gray-600'
              }`}>
                {message.role === 'user' ? (
                  <User className="w-4 h-4" />
                ) : (
                  <Bot className="w-4 h-4" />
                )}
              </div>
              
              <div className={`max-w-[70%] ${
                message.role === 'user' ? 'text-right' : ''
              }`}>
                <div className={`p-3 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-700 text-gray-100'
                }`}>
                  <div className="whitespace-pre-wrap">{message.content}</div>
                </div>
                
                <div className={`flex items-center gap-2 mt-1 text-xs text-gray-400 ${
                  message.role === 'user' ? 'justify-end' : ''
                }`}>
                  <span>{formatTime(new Date())}</span>
                  {message.role === 'assistant' && (
                    <button
                      onClick={() => copyMessage(message.content)}
                      className="hover:text-gray-300"
                      title="Copy message"
                    >
                      <Copy className="w-3 h-3" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {isTyping && (
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
                <Bot className="w-4 h-4" />
              </div>
              <div className="bg-gray-700 p-3 rounded-lg">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* File Attachments Preview */}
        {attachedFiles.length > 0 && (
          <div className="p-4 border-t border-gray-700 bg-gray-800">
            <div className="flex flex-wrap gap-2">
              {attachedFiles.map((file, index) => (
                <div key={index} className="flex items-center gap-2 bg-gray-700 p-2 rounded">
                  <span className="text-sm">{file.name}</span>
                  <button
                    onClick={() => removeFile(index)}
                    className="text-red-400 hover:text-red-300"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Input Area */}
        <div className="p-4 border-t border-gray-700 bg-gray-800">
          <div className="flex items-end gap-2">
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileSelect}
              multiple
              className="hidden"
            />
            
            <button
              onClick={() => fileInputRef.current?.click()}
              className="p-2 hover:bg-gray-700 rounded"
              title="Attach files"
            >
              <Paperclip className="w-5 h-5" />
            </button>
            
            <div className="flex-1">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                  }
                }}
                placeholder="Type your message... (Shift+Enter for new line)"
                className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg resize-none focus:outline-none focus:border-blue-500"
                rows={1}
                style={{ minHeight: '44px', maxHeight: '120px' }}
              />
            </div>
            
            <button
              onClick={sendMessage}
              disabled={!inputMessage.trim() && attachedFiles.length === 0}
              className="p-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded"
            >
              <Send className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chatbot;

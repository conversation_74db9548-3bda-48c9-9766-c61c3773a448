# Imagen AI Generator

A powerful desktop application for AI image generation with support for multiple AI services including Pollinations AI, Google ImageFX, and GPT4Free.

## Features

- **Multiple AI Services**: Support for Pollinations AI, ImageFX, and GPT4Free
- **Desktop Application**: Built with Electron for cross-platform compatibility
- **Image Management**: Save, organize, and manage generated images
- **Collections**: Create and manage image collections
- **Advanced Settings**: Customizable parameters for each AI service
- **Real-time Progress**: Live progress tracking for image generation

## AI Services

### Pollinations AI
- **Free**: No authentication required
- **Fast**: Quick image generation
- **Models**: Multiple models available (Flux, etc.)
- **Features**: Seed control, enhancement options, safety filters

### ImageFX (Google)
- **Authentication**: Requires OAuth token from Google Labs
- **Quality**: High-quality images using Imagen models
- **Models**: Imagen 2, Imagen 3, Imagen 4
- **Features**: Multiple aspect ratios, batch generation (1-8 images)

### GPT4Free (Unlimited Free Access)
- **Authentication**: No authentication required
- **Quality**: High-quality images using FLUX, DALL-E 3, Stable Diffusion XL
- **Features**: Multiple AI models, unlimited generation, no rate limits
- **Setup**: Works immediately - no configuration needed

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd imagenn
```

2. Install dependencies:
```bash
npm install
```

3. Run the application:
```bash
npm start
```

## Configuration

### ImageFX Setup
1. Visit [Google Labs ImageFX](https://labs.google.com/fx)
2. Sign in with your Google account
3. Open browser developer tools (F12)
4. Go to Application/Storage → Cookies
5. Find and copy the authentication token
6. Paste the token in the ImageFX settings

### GPT4Free Setup
**No setup required!** GPT4Free works immediately without any configuration:

1. **Select GPT4Free**: Choose "GPT4Free" as your AI service
2. **Choose Model**: Select from available models:
   - **FLUX** (Recommended) - Latest high-quality model
   - **DALL-E 3** - OpenAI's premium model
   - **Stable Diffusion XL** - Open-source excellence
   - **Playground v2.5** - Creative variations
   - **Ideogram** - Text-in-image specialist
3. **Set Parameters**: Adjust number of images and seed (optional)
4. **Generate**: Start creating unlimited images for free!

## Usage

1. **Select AI Service**: Choose from Pollinations AI, ImageFX, or GPT4Free
2. **Configure Settings**: Set up authentication and preferences for your chosen service
3. **Enter Prompt**: Describe the image you want to generate
4. **Generate**: Click the generate button and wait for results
5. **Manage Images**: Save, organize, and create collections of your generated images

## Advanced Features

### GPT4Free Features
- **Multiple Models**: Access to FLUX, DALL-E 3, Stable Diffusion XL, and more
- **Unlimited Generation**: No rate limits or usage quotas
- **High Quality**: Professional-grade AI image generation
- **No Authentication**: Works immediately without setup
- **Seed Control**: Reproducible results with custom seeds
- **Batch Generation**: Generate multiple images at once

### Image Management
- **Auto-save**: Generated images are automatically saved to local storage
- **Collections**: Organize images into themed collections
- **Filtering**: Filter images by service, date, or other criteria
- **Export**: Export images and collections

## Development

### Build Commands
```bash
# Development
npm run dev

# Build for production
npm run build

# Package for distribution
npm run dist
```

### Project Structure
```
src/
├── components/     # React components
├── hooks/         # Custom React hooks
├── utils/         # Utility functions and API services
├── types/         # TypeScript type definitions
└── main/          # Electron main process
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Disclaimer

This application uses reverse-engineered APIs for ImageFX and GPT4Free. Use at your own risk and in accordance with the respective services' terms of use.

## Support

For issues and questions, please create an issue in the GitHub repository.

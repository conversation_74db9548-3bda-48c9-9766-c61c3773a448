export interface ElectronAPI {
  // File download
  downloadImage: (imageData: string, filename: string) => Promise<{
    success: boolean
    path?: string
    error?: string
  }>
  
  // Settings operations
  saveSettings: (settings: any) => Promise<{
    success: boolean
    error?: string
  }>
  
  loadSettings: () => Promise<{
    success: boolean
    settings?: any
    error?: string
  }>
  
  // Generated images operations
  saveGeneratedImage: (imageData: string, imageInfo: any) => Promise<{
    success: boolean
    filename?: string
    filePath?: string
    error?: string
  }>
  
  loadGeneratedImages: () => Promise<{
    success: boolean
    images?: any[]
    error?: string
  }>
  
  deleteGeneratedImage: (filename: string) => Promise<{
    success: boolean
    error?: string
  }>

  // Refresh images manually
  refreshGeneratedImages: () => Promise<{
    success: boolean
    images?: any[]
    error?: string
  }>

  // Listen for file system changes
  onImagesChanged: (callback: () => void) => () => void

  // Prompt library operations
  savePrompts: (prompts: any[]) => Promise<{
    success: boolean
    error?: string
  }>

  loadPrompts: () => Promise<{
    success: boolean
    prompts?: any[]
    error?: string
  }>

  // Export operations
  exportImages: (imageData: any[]) => Promise<{
    success: boolean
    path?: string
    error?: string
  }>

  // Collections operations
  loadCollections: () => Promise<{
    success: boolean
    collections?: any[]
    error?: string
  }>

  saveCollections: (collections: any[]) => Promise<{
    success: boolean
    error?: string
  }>

  // Qwen Chat operations
  openQwenChat: () => Promise<{
    success: boolean
    error?: string
  }>

  clearQwenChatSession: () => Promise<{
    success: boolean
    error?: string
  }>

  // GPT4Free Chat operations
  gpt4freeChatGenerate: (request: any) => Promise<{
    success: boolean
    response?: any
    error?: string
  }>

  gpt4freeChatGetModels: () => Promise<{
    success: boolean
    models?: string[]
    error?: string
  }>

  gpt4freeChatGetProviders: () => Promise<{
    success: boolean
    providers?: string[]
    error?: string
  }>

  gpt4freeChatIsConfigured: () => Promise<{
    success: boolean
    configured?: boolean
    error?: string
  }>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

export {}

#!/bin/bash

echo "Installing Python dependencies for GPT4Free Chat Service..."
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "ERROR: Python is not installed or not in PATH"
    echo "Please install Python from https://python.org/downloads/"
    echo "Or use your system package manager:"
    echo "  Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "  macOS: brew install python3"
    echo "  CentOS/RHEL: sudo yum install python3 python3-pip"
    exit 1
fi

# Use python3 if available, otherwise python
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

echo "Python found. Installing dependencies..."
echo

# Install core dependencies
$PYTHON_CMD -m pip install g4f>=0.2.0
if [ $? -ne 0 ]; then
    echo "WARNING: Failed to install g4f. Trying alternative installation..."
    $PYTHON_CMD -m pip install --upgrade g4f
fi

# Install other dependencies
$PYTHON_CMD -m pip install aiohttp>=3.8.0 requests>=2.31.0 python-dotenv>=1.0.0

# Install optional dependencies (ignore errors)
echo
echo "Installing optional dependencies..."
$PYTHON_CMD -m pip install PyPDF2>=3.0.0 2>/dev/null || true
$PYTHON_CMD -m pip install Pillow>=10.0.0 2>/dev/null || true
$PYTHON_CMD -m pip install asyncio-throttle>=1.0.2 2>/dev/null || true

echo
echo "Setup complete! You can now use the GPT4Free chat service."
echo

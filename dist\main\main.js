"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
const gpt4free_chat_service_1 = require("./gpt4free-chat-service");
const https = __importStar(require("https"));
const http = __importStar(require("http"));
let mainWindow = null;
let qwenChatWindow = null;
const isDev = process.env.NODE_ENV === 'development';
// Get app directory (where the executable is located)
const getAppDirectory = () => {
    // In development, use current working directory
    // In production, use the directory where the executable is located
    if (process.env.NODE_ENV === 'development') {
        return process.cwd();
    }
    return path.dirname(process.execPath);
};
// Get generated images directory
const getGeneratedImagesPath = () => {
    const appDir = getAppDirectory();
    const imagesPath = path.join(appDir, 'generated-images');
    // Create directory if it doesn't exist
    if (!fs.existsSync(imagesPath)) {
        fs.mkdirSync(imagesPath, { recursive: true });
    }
    return imagesPath;
};
// Get settings file path
const getSettingsPath = () => {
    const appDir = getAppDirectory();
    return path.join(appDir, 'settings.json');
};
// Get prompts file path
const getPromptsPath = () => {
    const appDir = getAppDirectory();
    return path.join(appDir, 'prompts.json');
};
// Get collections file path
const getCollectionsPath = () => {
    const appDir = getAppDirectory();
    return path.join(appDir, 'collections.json');
};
// Get Qwen Chat session data path
const getQwenChatSessionPath = () => {
    const appDir = getAppDirectory();
    const sessionDir = path.join(appDir, 'qwen-chat-session');
    // Create directory if it doesn't exist
    if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
    }
    return sessionDir;
};
function createWindow() {
    // Create the browser window
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 900,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: path.join(__dirname, '../preload/preload.js'),
        },
        titleBarStyle: 'default',
        backgroundColor: '#0a0a0a',
        show: false, // Don't show until ready
    });
    // Show window when ready to prevent visual flash
    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
        // Setup file system watcher after window is ready
        setupImageWatcher();
    });
    // Load the app
    if (isDev) {
        mainWindow.loadURL('http://localhost:5173');
        // Open DevTools in development
        mainWindow.webContents.openDevTools();
    }
    else {
        mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }
    // Set up menu
    const template = [
        {
            label: 'File',
            submenu: [
                {
                    label: 'Quit',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        electron_1.app.quit();
                    }
                }
            ]
        },
        {
            label: 'Edit',
            submenu: [
                { role: 'undo' },
                { role: 'redo' },
                { type: 'separator' },
                { role: 'cut' },
                { role: 'copy' },
                { role: 'paste' }
            ]
        },
        {
            label: 'View',
            submenu: [
                { role: 'reload' },
                { role: 'forceReload' },
                { role: 'toggleDevTools' },
                { type: 'separator' },
                { role: 'resetZoom' },
                { role: 'zoomIn' },
                { role: 'zoomOut' },
                { type: 'separator' },
                { role: 'togglefullscreen' }
            ]
        },
        {
            label: 'Qwen Chat',
            submenu: [
                {
                    label: 'Open Qwen Chat',
                    accelerator: 'CmdOrCtrl+Shift+Q',
                    click: () => {
                        createQwenChatWindow();
                    }
                },
                {
                    label: 'Clear Session Data',
                    click: async () => {
                        const response = await electron_1.dialog.showMessageBox(mainWindow, {
                            type: 'warning',
                            buttons: ['Cancel', 'Clear'],
                            defaultId: 0,
                            title: 'Clear Qwen Chat Session',
                            message: 'Are you sure you want to clear all Qwen Chat session data?',
                            detail: 'This will log you out and remove all stored login information.'
                        });
                        if (response.response === 1) {
                            try {
                                // Close Qwen Chat window if open
                                if (qwenChatWindow && !qwenChatWindow.isDestroyed()) {
                                    qwenChatWindow.close();
                                }
                                // Clear session data
                                const qwenSession = electron_1.session.fromPartition('persist:qwen-chat');
                                await qwenSession.clearStorageData();
                                // Remove session directory
                                const sessionPath = getQwenChatSessionPath();
                                if (fs.existsSync(sessionPath)) {
                                    fs.rmSync(sessionPath, { recursive: true, force: true });
                                }
                                electron_1.dialog.showMessageBox(mainWindow, {
                                    type: 'info',
                                    title: 'Session Cleared',
                                    message: 'Qwen Chat session data has been cleared successfully.'
                                });
                            }
                            catch (error) {
                                electron_1.dialog.showErrorBox('Error', 'Failed to clear session data: ' + error.message);
                            }
                        }
                    }
                }
            ]
        }
    ];
    const menu = electron_1.Menu.buildFromTemplate(template);
    electron_1.Menu.setApplicationMenu(menu);
}
function createQwenChatWindow() {
    // Don't create multiple instances
    if (qwenChatWindow && !qwenChatWindow.isDestroyed()) {
        qwenChatWindow.focus();
        return;
    }
    // Create a persistent session for Qwen Chat
    const qwenSession = electron_1.session.fromPartition('persist:qwen-chat', {
        cache: true
    });
    // Set up session data directory
    getQwenChatSessionPath();
    // Create the Qwen Chat window
    qwenChatWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            session: qwenSession,
            webSecurity: false,
            allowRunningInsecureContent: true,
            backgroundThrottling: false,
        },
        titleBarStyle: 'default',
        backgroundColor: '#ffffff',
        title: 'Qwen Chat',
        show: false,
    });
    // Show window when ready
    qwenChatWindow.once('ready-to-show', () => {
        qwenChatWindow?.show();
    });
    // Add debugging for load events
    qwenChatWindow.webContents.on('did-start-loading', () => {
        console.log('Qwen Chat: Started loading');
    });
    qwenChatWindow.webContents.on('did-finish-load', () => {
        console.log('Qwen Chat: Finished loading');
    });
    qwenChatWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription, validatedURL) => {
        console.error('Qwen Chat: Failed to load', errorCode, errorDescription, validatedURL);
    });
    // Handle crashes and unresponsive pages
    qwenChatWindow.webContents.on('render-process-gone', (_, details) => {
        console.error('Qwen Chat: Render process gone', details);
    });
    qwenChatWindow.webContents.on('unresponsive', () => {
        console.warn('Qwen Chat: Page became unresponsive');
    });
    qwenChatWindow.webContents.on('responsive', () => {
        console.log('Qwen Chat: Page became responsive again');
    });
    // Load the local HTML file with iframe
    const qwenChatHtmlPath = isDev
        ? path.join(__dirname, '../../src/qwen-chat.html')
        : path.join(__dirname, '../qwen-chat.html');
    qwenChatWindow.loadFile(qwenChatHtmlPath);
    // Handle window closed
    qwenChatWindow.on('closed', () => {
        qwenChatWindow = null;
    });
    // Set up user agent to avoid detection issues
    qwenChatWindow.webContents.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
    // Handle navigation to prevent white screen issues
    qwenChatWindow.webContents.on('will-navigate', (event, navigationUrl) => {
        console.log('Qwen Chat: Navigation attempt to:', navigationUrl);
        // Allow navigation within qwen.ai domain
        try {
            const url = new URL(navigationUrl);
            if (url.hostname.includes('qwen.ai')) {
                console.log('Qwen Chat: Allowing navigation within qwen.ai domain');
                return;
            }
            // Prevent navigation to external sites
            console.log('Qwen Chat: Preventing external navigation, opening in browser instead');
            event.preventDefault();
            require('electron').shell.openExternal(navigationUrl);
        }
        catch (error) {
            console.error('Qwen Chat: Error handling navigation:', error);
            event.preventDefault();
        }
    });
    // Handle new window requests
    qwenChatWindow.webContents.setWindowOpenHandler(({ url }) => {
        console.log('Qwen Chat: New window request for:', url);
        try {
            const urlObj = new URL(url);
            if (urlObj.hostname.includes('qwen.ai')) {
                return { action: 'allow' };
            }
            // Open external links in default browser
            require('electron').shell.openExternal(url);
            return { action: 'deny' };
        }
        catch (error) {
            console.error('Qwen Chat: Error handling new window:', error);
            return { action: 'deny' };
        }
    });
    // Handle window close
    qwenChatWindow.on('closed', () => {
        qwenChatWindow = null;
    });
}
// Handle file download
electron_1.ipcMain.handle('download-image', async (_event, imageData, filename) => {
    try {
        // Validate inputs
        if (!imageData || !filename) {
            return { success: false, error: 'Invalid image data or filename' };
        }
        const { filePath, canceled } = await electron_1.dialog.showSaveDialog({
            defaultPath: filename,
            filters: [
                { name: 'PNG Images', extensions: ['png'] },
                { name: 'JPEG Images', extensions: ['jpg', 'jpeg'] },
                { name: 'All Images', extensions: ['png', 'jpg', 'jpeg'] }
            ]
        });
        if (canceled || !filePath) {
            return { success: false, error: 'Save cancelled by user' };
        }
        // Remove data URL prefix if present
        const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
        // Validate base64 data
        if (!base64Data) {
            return { success: false, error: 'Invalid image data format' };
        }
        const buffer = Buffer.from(base64Data, 'base64');
        // Check if buffer is valid
        if (buffer.length === 0) {
            return { success: false, error: 'Empty image data' };
        }
        fs.writeFileSync(filePath, buffer);
        return { success: true, path: filePath };
    }
    catch (error) {
        console.error('Download error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
// Helper function to fetch image from URL
const fetchImageFromUrl = (url) => {
    return new Promise((resolve, reject) => {
        const protocol = url.startsWith('https:') ? https : http;
        protocol.get(url, (response) => {
            if (response.statusCode !== 200) {
                reject(new Error(`Failed to fetch image: ${response.statusCode}`));
                return;
            }
            const chunks = [];
            response.on('data', (chunk) => chunks.push(chunk));
            response.on('end', () => resolve(Buffer.concat(chunks)));
            response.on('error', reject);
        }).on('error', reject);
    });
};
// Helper function to detect image format from buffer
const detectImageFormat = (buffer) => {
    // Check PNG signature
    if (buffer.length >= 8 && buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
        return { format: 'png', mimeType: 'image/png' };
    }
    // Check JPEG signature
    if (buffer.length >= 2 && buffer[0] === 0xFF && buffer[1] === 0xD8) {
        return { format: 'jpg', mimeType: 'image/jpeg' };
    }
    // Check WebP signature
    if (buffer.length >= 12 && buffer.toString('ascii', 0, 4) === 'RIFF' && buffer.toString('ascii', 8, 12) === 'WEBP') {
        return { format: 'webp', mimeType: 'image/webp' };
    }
    // Default to PNG
    return { format: 'png', mimeType: 'image/png' };
};
// File system watcher for generated images
let imageWatcher = null;
const setupImageWatcher = () => {
    if (imageWatcher) {
        imageWatcher.close();
    }
    const imagesPath = getGeneratedImagesPath();
    try {
        imageWatcher = fs.watch(imagesPath, (eventType, filename) => {
            if (filename && (filename.endsWith('.png') || filename.endsWith('.jpg') || filename.endsWith('.jpeg') || filename.endsWith('.webp'))) {
                console.log(`File system change detected: ${eventType} - ${filename}`);
                // Notify renderer process about file changes
                if (mainWindow && !mainWindow.isDestroyed()) {
                    mainWindow.webContents.send('images-changed');
                }
            }
        });
        console.log('Image watcher setup successfully');
    }
    catch (error) {
        console.error('Failed to setup image watcher:', error);
    }
};
// Handle settings operations
electron_1.ipcMain.handle('save-settings', async (_event, settings) => {
    try {
        const settingsPath = getSettingsPath();
        fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2));
        return { success: true };
    }
    catch (error) {
        console.error('Save settings error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
electron_1.ipcMain.handle('load-settings', async () => {
    try {
        const settingsPath = getSettingsPath();
        if (fs.existsSync(settingsPath)) {
            const settingsData = fs.readFileSync(settingsPath, 'utf8');
            const settings = JSON.parse(settingsData);
            return { success: true, settings };
        }
        return { success: true, settings: null };
    }
    catch (error) {
        console.error('Load settings error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
// Handle image storage operations
electron_1.ipcMain.handle('save-generated-image', async (_event, imageData, imageInfo) => {
    try {
        const imagesPath = getGeneratedImagesPath();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
        let buffer;
        let imageFormat;
        let mimeType;
        // Check if imageData is a URL or base64 data
        if (imageData.startsWith('http://') || imageData.startsWith('https://')) {
            // Fetch image from URL
            console.log('Fetching image from URL:', imageData);
            buffer = await fetchImageFromUrl(imageData);
            // Detect format from buffer
            const detected = detectImageFormat(buffer);
            imageFormat = detected.format;
            mimeType = detected.mimeType;
        }
        else if (imageData.startsWith('data:image/')) {
            // Handle base64 data URL
            if (imageData.startsWith('data:image/jpeg') || imageData.startsWith('data:image/jpg')) {
                imageFormat = 'jpg';
                mimeType = 'image/jpeg';
            }
            else if (imageData.startsWith('data:image/webp')) {
                imageFormat = 'webp';
                mimeType = 'image/webp';
            }
            else {
                imageFormat = 'png';
                mimeType = 'image/png';
            }
            const base64Data = imageData.replace(/^data:image\/[a-z]+;base64,/, '');
            buffer = Buffer.from(base64Data, 'base64');
        }
        else {
            throw new Error('Invalid image data format');
        }
        // Validate buffer
        if (!buffer || buffer.length === 0) {
            throw new Error('Empty or invalid image buffer');
        }
        const filename = `${imageInfo.service}-${timestamp}-${imageInfo.id}.${imageFormat}`;
        const filePath = path.join(imagesPath, filename);
        fs.writeFileSync(filePath, buffer);
        console.log(`Image saved successfully: ${filename} (${buffer.length} bytes)`);
        // Save metadata with format info
        const metadataPath = path.join(imagesPath, `${filename}.json`);
        const metadataWithFormat = {
            ...imageInfo,
            imageFormat,
            mimeType,
            fileSize: buffer.length
        };
        fs.writeFileSync(metadataPath, JSON.stringify(metadataWithFormat, null, 2));
        return { success: true, filename, filePath };
    }
    catch (error) {
        console.error('Save generated image error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
electron_1.ipcMain.handle('load-generated-images', async () => {
    try {
        const imagesPath = getGeneratedImagesPath();
        const files = fs.readdirSync(imagesPath);
        const images = [];
        for (const file of files) {
            if (file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg') || file.endsWith('.webp')) {
                try {
                    const metadataFile = `${file}.json`;
                    const metadataPath = path.join(imagesPath, metadataFile);
                    if (fs.existsSync(metadataPath)) {
                        const metadataContent = fs.readFileSync(metadataPath, 'utf8');
                        const metadata = JSON.parse(metadataContent);
                        const imagePath = path.join(imagesPath, file);
                        // Check if image file still exists
                        if (!fs.existsSync(imagePath)) {
                            console.warn(`Image file not found: ${file}`);
                            continue;
                        }
                        const imageBuffer = fs.readFileSync(imagePath);
                        // Determine MIME type from file extension or metadata
                        let mimeType = metadata.mimeType || 'image/png';
                        if (file.endsWith('.jpg') || file.endsWith('.jpeg')) {
                            mimeType = 'image/jpeg';
                        }
                        else if (file.endsWith('.webp')) {
                            mimeType = 'image/webp';
                        }
                        const base64Image = `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
                        images.push({
                            ...metadata,
                            url: base64Image,
                            filename: file
                        });
                    }
                }
                catch (error) {
                    console.error(`Failed to load image ${file}:`, error);
                    // Continue with other images instead of failing completely
                }
            }
        }
        // Sort by timestamp (newest first)
        images.sort((a, b) => b.timestamp - a.timestamp);
        return { success: true, images };
    }
    catch (error) {
        console.error('Load generated images error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
electron_1.ipcMain.handle('delete-generated-image', async (_event, filename) => {
    try {
        const imagesPath = getGeneratedImagesPath();
        const imagePath = path.join(imagesPath, filename);
        const metadataPath = path.join(imagesPath, `${filename}.json`);
        // Delete image file
        if (fs.existsSync(imagePath)) {
            fs.unlinkSync(imagePath);
        }
        // Delete metadata file
        if (fs.existsSync(metadataPath)) {
            fs.unlinkSync(metadataPath);
        }
        return { success: true };
    }
    catch (error) {
        console.error('Delete generated image error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
// Handle manual refresh of images
electron_1.ipcMain.handle('refresh-generated-images', async () => {
    // Re-use the existing load logic
    try {
        const imagesPath = getGeneratedImagesPath();
        const files = fs.readdirSync(imagesPath);
        const images = [];
        for (const file of files) {
            if (file.endsWith('.png') || file.endsWith('.jpg') || file.endsWith('.jpeg') || file.endsWith('.webp')) {
                const metadataFile = `${file}.json`;
                const metadataPath = path.join(imagesPath, metadataFile);
                if (fs.existsSync(metadataPath)) {
                    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
                    const imagePath = path.join(imagesPath, file);
                    const imageBuffer = fs.readFileSync(imagePath);
                    // Determine MIME type from file extension or metadata
                    let mimeType = metadata.mimeType || 'image/png';
                    if (file.endsWith('.jpg') || file.endsWith('.jpeg')) {
                        mimeType = 'image/jpeg';
                    }
                    else if (file.endsWith('.webp')) {
                        mimeType = 'image/webp';
                    }
                    const base64Image = `data:${mimeType};base64,${imageBuffer.toString('base64')}`;
                    images.push({
                        ...metadata,
                        url: base64Image,
                        filename: file
                    });
                }
            }
        }
        // Sort by timestamp (newest first)
        images.sort((a, b) => b.timestamp - a.timestamp);
        return { success: true, images };
    }
    catch (error) {
        console.error('Refresh generated images error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
// Handle prompt library operations
electron_1.ipcMain.handle('save-prompts', async (_event, prompts) => {
    try {
        const promptsPath = getPromptsPath();
        fs.writeFileSync(promptsPath, JSON.stringify(prompts, null, 2));
        return { success: true };
    }
    catch (error) {
        console.error('Save prompts error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
electron_1.ipcMain.handle('load-prompts', async () => {
    try {
        const promptsPath = getPromptsPath();
        if (!fs.existsSync(promptsPath)) {
            return { success: true, prompts: [] };
        }
        const data = fs.readFileSync(promptsPath, 'utf8');
        const prompts = JSON.parse(data);
        return { success: true, prompts };
    }
    catch (error) {
        console.error('Load prompts error:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage, prompts: [] };
    }
});
// Handle batch export of images
electron_1.ipcMain.handle('export-images', async (_event, imageData) => {
    try {
        if (!imageData || imageData.length === 0) {
            return { success: false, error: 'No images to export' };
        }
        // Show save dialog to choose export location
        const result = await electron_1.dialog.showSaveDialog(mainWindow, {
            title: 'Export Images',
            defaultPath: `imagen-export-${new Date().toISOString().slice(0, 10)}.zip`,
            filters: [
                { name: 'ZIP Archive', extensions: ['zip'] },
                { name: 'All Files', extensions: ['*'] }
            ]
        });
        if (result.canceled || !result.filePath) {
            return { success: false, error: 'Export canceled' };
        }
        // For now, create a simple folder export instead of ZIP
        // In a production app, you'd want to use a proper ZIP library
        const exportPath = result.filePath.replace('.zip', '-folder');
        // Create export directory
        if (!fs.existsSync(exportPath)) {
            fs.mkdirSync(exportPath, { recursive: true });
        }
        let successCount = 0;
        const exportManifest = [];
        for (let i = 0; i < imageData.length; i++) {
            const image = imageData[i];
            try {
                // Copy image file if it exists locally
                if (image.filename) {
                    const sourcePath = path.join(getGeneratedImagesPath(), image.filename);
                    if (fs.existsSync(sourcePath)) {
                        const destPath = path.join(exportPath, image.filename);
                        fs.copyFileSync(sourcePath, destPath);
                        // Copy metadata file if it exists
                        const metadataSource = path.join(getGeneratedImagesPath(), `${image.filename}.json`);
                        if (fs.existsSync(metadataSource)) {
                            const metadataDest = path.join(exportPath, `${image.filename}.json`);
                            fs.copyFileSync(metadataSource, metadataDest);
                        }
                        successCount++;
                        exportManifest.push({
                            filename: image.filename,
                            prompt: image.prompt,
                            service: image.service,
                            timestamp: image.timestamp,
                            exported: true
                        });
                    }
                }
            }
            catch (error) {
                console.error(`Failed to export image ${image.id}:`, error);
                exportManifest.push({
                    filename: image.filename || `image-${i}`,
                    prompt: image.prompt,
                    service: image.service,
                    timestamp: image.timestamp,
                    exported: false,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
        // Create export manifest
        const manifestPath = path.join(exportPath, 'export-manifest.json');
        fs.writeFileSync(manifestPath, JSON.stringify({
            exportDate: new Date().toISOString(),
            totalImages: imageData.length,
            successfulExports: successCount,
            images: exportManifest
        }, null, 2));
        if (successCount > 0) {
            return { success: true, path: exportPath };
        }
        else {
            return { success: false, error: 'No images could be exported' };
        }
    }
    catch (error) {
        console.error('Failed to export images:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
// Handle Qwen Chat operations
electron_1.ipcMain.handle('open-qwen-chat', async () => {
    try {
        createQwenChatWindow();
        return { success: true };
    }
    catch (error) {
        console.error('Failed to open Qwen Chat:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
electron_1.ipcMain.handle('clear-qwen-chat-session', async () => {
    try {
        // Close Qwen Chat window if open
        if (qwenChatWindow && !qwenChatWindow.isDestroyed()) {
            qwenChatWindow.close();
        }
        // Clear session data
        const qwenSession = electron_1.session.fromPartition('persist:qwen-chat');
        await qwenSession.clearStorageData();
        // Remove session directory
        const sessionPath = getQwenChatSessionPath();
        if (fs.existsSync(sessionPath)) {
            fs.rmSync(sessionPath, { recursive: true, force: true });
        }
        return { success: true };
    }
    catch (error) {
        console.error('Failed to clear Qwen Chat session:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
// Handle collections operations
electron_1.ipcMain.handle('load-collections', async () => {
    try {
        const collectionsPath = getCollectionsPath();
        if (!fs.existsSync(collectionsPath)) {
            return { success: true, collections: [] };
        }
        const collectionsData = fs.readFileSync(collectionsPath, 'utf8');
        const collections = JSON.parse(collectionsData);
        return { success: true, collections };
    }
    catch (error) {
        console.error('Failed to load collections:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
electron_1.ipcMain.handle('save-collections', async (_event, collections) => {
    try {
        const collectionsPath = getCollectionsPath();
        fs.writeFileSync(collectionsPath, JSON.stringify(collections, null, 2));
        return { success: true };
    }
    catch (error) {
        console.error('Failed to save collections:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        return { success: false, error: errorMessage };
    }
});
// This method will be called when Electron has finished initialization
electron_1.app.whenReady().then(() => {
    createWindow();
    electron_1.app.on('activate', () => {
        // On macOS, re-create a window when the dock icon is clicked
        if (electron_1.BrowserWindow.getAllWindows().length === 0)
            createWindow();
    });
});
// Quit when all windows are closed, except on macOS
electron_1.app.on('window-all-closed', () => {
    // Clean up file watcher
    if (imageWatcher) {
        imageWatcher.close();
        imageWatcher = null;
    }
    if (process.platform !== 'darwin')
        electron_1.app.quit();
});
// Handle GPT4Free Chat operations
electron_1.ipcMain.handle('gpt4free-chat-generate', async (_event, request) => {
    try {
        const response = await gpt4free_chat_service_1.gpt4freeChatService.generateResponse(request);
        return { success: true, response };
    }
    catch (error) {
        console.error('GPT4Free chat generation failed:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error occurred'
        };
    }
});
electron_1.ipcMain.handle('gpt4free-chat-models', async () => {
    try {
        const models = await gpt4free_chat_service_1.gpt4freeChatService.getAvailableModels();
        return { success: true, models };
    }
    catch (error) {
        console.error('Failed to get GPT4Free models:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error occurred',
            models: ['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus'] // fallback
        };
    }
});
electron_1.ipcMain.handle('gpt4free-chat-providers', async () => {
    try {
        const providers = await gpt4free_chat_service_1.gpt4freeChatService.getAvailableProviders();
        return { success: true, providers };
    }
    catch (error) {
        console.error('Failed to get GPT4Free providers:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error occurred',
            providers: ['auto', 'bing', 'you', 'phind'] // fallback
        };
    }
});
electron_1.ipcMain.handle('gpt4free-chat-configured', async () => {
    try {
        const configured = gpt4free_chat_service_1.gpt4freeChatService.isServiceConfigured();
        return { success: true, configured };
    }
    catch (error) {
        console.error('Failed to check GPT4Free configuration:', error);
        return { success: false, configured: false };
    }
});
// Clean up on app quit
electron_1.app.on('before-quit', () => {
    if (imageWatcher) {
        imageWatcher.close();
        imageWatcher = null;
    }
});

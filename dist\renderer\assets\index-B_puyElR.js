var Fd=Object.defineProperty;var Zo=e=>{throw TypeError(e)};var Rd=(e,t,n)=>t in e?Fd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ln=(e,t,n)=>Rd(e,typeof t!="symbol"?t+"":t,n),Od=(e,t,n)=>t.has(e)||Zo("Cannot "+n);var qo=(e,t,n)=>t.has(e)?Zo("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n);var Jo=(e,t,n)=>(Od(e,t,"access private method"),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))r(l);new MutationObserver(l=>{for(const a of l)if(a.type==="childList")for(const o of a.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(l){const a={};return l.integrity&&(a.integrity=l.integrity),l.referrerPolicy&&(a.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?a.credentials="include":l.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function r(l){if(l.ep)return;l.ep=!0;const a=n(l);fetch(l.href,a)}})();function $d(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var vc={exports:{}},rs={},wc={exports:{}},ee={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hr=Symbol.for("react.element"),Ud=Symbol.for("react.portal"),Hd=Symbol.for("react.fragment"),Bd=Symbol.for("react.strict_mode"),Vd=Symbol.for("react.profiler"),Wd=Symbol.for("react.provider"),Gd=Symbol.for("react.context"),Xd=Symbol.for("react.forward_ref"),Qd=Symbol.for("react.suspense"),Kd=Symbol.for("react.memo"),Yd=Symbol.for("react.lazy"),ei=Symbol.iterator;function Zd(e){return e===null||typeof e!="object"?null:(e=ei&&e[ei]||e["@@iterator"],typeof e=="function"?e:null)}var jc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Nc=Object.assign,kc={};function Jn(e,t,n){this.props=e,this.context=t,this.refs=kc,this.updater=n||jc}Jn.prototype.isReactComponent={};Jn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Jn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function bc(){}bc.prototype=Jn.prototype;function Za(e,t,n){this.props=e,this.context=t,this.refs=kc,this.updater=n||jc}var qa=Za.prototype=new bc;qa.constructor=Za;Nc(qa,Jn.prototype);qa.isPureReactComponent=!0;var ti=Array.isArray,Cc=Object.prototype.hasOwnProperty,Ja={current:null},Sc={key:!0,ref:!0,__self:!0,__source:!0};function Ec(e,t,n){var r,l={},a=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(a=""+t.key),t)Cc.call(t,r)&&!Sc.hasOwnProperty(r)&&(l[r]=t[r]);var i=arguments.length-2;if(i===1)l.children=n;else if(1<i){for(var c=Array(i),u=0;u<i;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(r in i=e.defaultProps,i)l[r]===void 0&&(l[r]=i[r]);return{$$typeof:Hr,type:e,key:a,ref:o,props:l,_owner:Ja.current}}function qd(e,t){return{$$typeof:Hr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function eo(e){return typeof e=="object"&&e!==null&&e.$$typeof===Hr}function Jd(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ni=/\/+/g;function Ps(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Jd(""+e.key):t.toString(36)}function yl(e,t,n,r,l){var a=typeof e;(a==="undefined"||a==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(a){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case Hr:case Ud:o=!0}}if(o)return o=e,l=l(o),e=r===""?"."+Ps(o,0):r,ti(l)?(n="",e!=null&&(n=e.replace(ni,"$&/")+"/"),yl(l,t,n,"",function(u){return u})):l!=null&&(eo(l)&&(l=qd(l,n+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(ni,"$&/")+"/")+e)),t.push(l)),1;if(o=0,r=r===""?".":r+":",ti(e))for(var i=0;i<e.length;i++){a=e[i];var c=r+Ps(a,i);o+=yl(a,t,n,c,l)}else if(c=Zd(e),typeof c=="function")for(e=c.call(e),i=0;!(a=e.next()).done;)a=a.value,c=r+Ps(a,i++),o+=yl(a,t,n,c,l);else if(a==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function el(e,t,n){if(e==null)return e;var r=[],l=0;return yl(e,r,"","",function(a){return t.call(n,a,l++)}),r}function ef(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var $e={current:null},vl={transition:null},tf={ReactCurrentDispatcher:$e,ReactCurrentBatchConfig:vl,ReactCurrentOwner:Ja};function Ic(){throw Error("act(...) is not supported in production builds of React.")}ee.Children={map:el,forEach:function(e,t,n){el(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return el(e,function(){t++}),t},toArray:function(e){return el(e,function(t){return t})||[]},only:function(e){if(!eo(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ee.Component=Jn;ee.Fragment=Hd;ee.Profiler=Vd;ee.PureComponent=Za;ee.StrictMode=Bd;ee.Suspense=Qd;ee.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=tf;ee.act=Ic;ee.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=Nc({},e.props),l=e.key,a=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(a=t.ref,o=Ja.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var i=e.type.defaultProps;for(c in t)Cc.call(t,c)&&!Sc.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&i!==void 0?i[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){i=Array(c);for(var u=0;u<c;u++)i[u]=arguments[u+2];r.children=i}return{$$typeof:Hr,type:e.type,key:l,ref:a,props:r,_owner:o}};ee.createContext=function(e){return e={$$typeof:Gd,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Wd,_context:e},e.Consumer=e};ee.createElement=Ec;ee.createFactory=function(e){var t=Ec.bind(null,e);return t.type=e,t};ee.createRef=function(){return{current:null}};ee.forwardRef=function(e){return{$$typeof:Xd,render:e}};ee.isValidElement=eo;ee.lazy=function(e){return{$$typeof:Yd,_payload:{_status:-1,_result:e},_init:ef}};ee.memo=function(e,t){return{$$typeof:Kd,type:e,compare:t===void 0?null:t}};ee.startTransition=function(e){var t=vl.transition;vl.transition={};try{e()}finally{vl.transition=t}};ee.unstable_act=Ic;ee.useCallback=function(e,t){return $e.current.useCallback(e,t)};ee.useContext=function(e){return $e.current.useContext(e)};ee.useDebugValue=function(){};ee.useDeferredValue=function(e){return $e.current.useDeferredValue(e)};ee.useEffect=function(e,t){return $e.current.useEffect(e,t)};ee.useId=function(){return $e.current.useId()};ee.useImperativeHandle=function(e,t,n){return $e.current.useImperativeHandle(e,t,n)};ee.useInsertionEffect=function(e,t){return $e.current.useInsertionEffect(e,t)};ee.useLayoutEffect=function(e,t){return $e.current.useLayoutEffect(e,t)};ee.useMemo=function(e,t){return $e.current.useMemo(e,t)};ee.useReducer=function(e,t,n){return $e.current.useReducer(e,t,n)};ee.useRef=function(e){return $e.current.useRef(e)};ee.useState=function(e){return $e.current.useState(e)};ee.useSyncExternalStore=function(e,t,n){return $e.current.useSyncExternalStore(e,t,n)};ee.useTransition=function(){return $e.current.useTransition()};ee.version="18.3.1";wc.exports=ee;var N=wc.exports;const nf=$d(N);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var rf=N,lf=Symbol.for("react.element"),sf=Symbol.for("react.fragment"),af=Object.prototype.hasOwnProperty,of=rf.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,cf={key:!0,ref:!0,__self:!0,__source:!0};function Pc(e,t,n){var r,l={},a=null,o=null;n!==void 0&&(a=""+n),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)af.call(t,r)&&!cf.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)l[r]===void 0&&(l[r]=t[r]);return{$$typeof:lf,type:e,key:a,ref:o,props:l,_owner:of.current}}rs.Fragment=sf;rs.jsx=Pc;rs.jsxs=Pc;vc.exports=rs;var s=vc.exports,ra={},zc={exports:{}},tt={},Mc={exports:{}},_c={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(O,$){var D=O.length;O.push($);e:for(;0<D;){var ne=D-1>>>1,Z=O[ne];if(0<l(Z,$))O[ne]=$,O[D]=Z,D=ne;else break e}}function n(O){return O.length===0?null:O[0]}function r(O){if(O.length===0)return null;var $=O[0],D=O.pop();if(D!==$){O[0]=D;e:for(var ne=0,Z=O.length,we=Z>>>1;ne<we;){var C=2*(ne+1)-1,_=O[C],A=C+1,J=O[A];if(0>l(_,D))A<Z&&0>l(J,_)?(O[ne]=J,O[A]=D,ne=A):(O[ne]=_,O[C]=D,ne=C);else if(A<Z&&0>l(J,D))O[ne]=J,O[A]=D,ne=A;else break e}}return $}function l(O,$){var D=O.sortIndex-$.sortIndex;return D!==0?D:O.id-$.id}if(typeof performance=="object"&&typeof performance.now=="function"){var a=performance;e.unstable_now=function(){return a.now()}}else{var o=Date,i=o.now();e.unstable_now=function(){return o.now()-i}}var c=[],u=[],x=1,y=null,g=3,j=!1,w=!1,k=!1,U=typeof setTimeout=="function"?setTimeout:null,f=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(O){for(var $=n(u);$!==null;){if($.callback===null)r(u);else if($.startTime<=O)r(u),$.sortIndex=$.expirationTime,t(c,$);else break;$=n(u)}}function p(O){if(k=!1,m(O),!w)if(n(c)!==null)w=!0,ge(b);else{var $=n(u);$!==null&&ct(p,$.startTime-O)}}function b(O,$){w=!1,k&&(k=!1,f(E),E=-1),j=!0;var D=g;try{for(m($),y=n(c);y!==null&&(!(y.expirationTime>$)||O&&!q());){var ne=y.callback;if(typeof ne=="function"){y.callback=null,g=y.priorityLevel;var Z=ne(y.expirationTime<=$);$=e.unstable_now(),typeof Z=="function"?y.callback=Z:y===n(c)&&r(c),m($)}else r(c);y=n(c)}if(y!==null)var we=!0;else{var C=n(u);C!==null&&ct(p,C.startTime-$),we=!1}return we}finally{y=null,g=D,j=!1}}var M=!1,F=null,E=-1,B=5,L=-1;function q(){return!(e.unstable_now()-L<B)}function ve(){if(F!==null){var O=e.unstable_now();L=O;var $=!0;try{$=F(!0,O)}finally{$?G():(M=!1,F=null)}}else M=!1}var G;if(typeof d=="function")G=function(){d(ve)};else if(typeof MessageChannel<"u"){var ae=new MessageChannel,ie=ae.port2;ae.port1.onmessage=ve,G=function(){ie.postMessage(null)}}else G=function(){U(ve,0)};function ge(O){F=O,M||(M=!0,G())}function ct(O,$){E=U(function(){O(e.unstable_now())},$)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(O){O.callback=null},e.unstable_continueExecution=function(){w||j||(w=!0,ge(b))},e.unstable_forceFrameRate=function(O){0>O||125<O?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):B=0<O?Math.floor(1e3/O):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(O){switch(g){case 1:case 2:case 3:var $=3;break;default:$=g}var D=g;g=$;try{return O()}finally{g=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(O,$){switch(O){case 1:case 2:case 3:case 4:case 5:break;default:O=3}var D=g;g=O;try{return $()}finally{g=D}},e.unstable_scheduleCallback=function(O,$,D){var ne=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?ne+D:ne):D=ne,O){case 1:var Z=-1;break;case 2:Z=250;break;case 5:Z=**********;break;case 4:Z=1e4;break;default:Z=5e3}return Z=D+Z,O={id:x++,callback:$,priorityLevel:O,startTime:D,expirationTime:Z,sortIndex:-1},D>ne?(O.sortIndex=D,t(u,O),n(c)===null&&O===n(u)&&(k?(f(E),E=-1):k=!0,ct(p,D-ne))):(O.sortIndex=Z,t(c,O),w||j||(w=!0,ge(b))),O},e.unstable_shouldYield=q,e.unstable_wrapCallback=function(O){var $=g;return function(){var D=g;g=$;try{return O.apply(this,arguments)}finally{g=D}}}})(_c);Mc.exports=_c;var uf=Mc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var df=N,et=uf;function z(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Ac=new Set,Cr={};function wn(e,t){Gn(e,t),Gn(e+"Capture",t)}function Gn(e,t){for(Cr[e]=t,e=0;e<t.length;e++)Ac.add(t[e])}var zt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),la=Object.prototype.hasOwnProperty,ff=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ri={},li={};function mf(e){return la.call(li,e)?!0:la.call(ri,e)?!1:ff.test(e)?li[e]=!0:(ri[e]=!0,!1)}function pf(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function hf(e,t,n,r){if(t===null||typeof t>"u"||pf(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ue(e,t,n,r,l,a,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=o}var _e={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){_e[e]=new Ue(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];_e[t]=new Ue(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){_e[e]=new Ue(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){_e[e]=new Ue(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){_e[e]=new Ue(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){_e[e]=new Ue(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){_e[e]=new Ue(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){_e[e]=new Ue(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){_e[e]=new Ue(e,5,!1,e.toLowerCase(),null,!1,!1)});var to=/[\-:]([a-z])/g;function no(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(to,no);_e[t]=new Ue(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(to,no);_e[t]=new Ue(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(to,no);_e[t]=new Ue(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){_e[e]=new Ue(e,1,!1,e.toLowerCase(),null,!1,!1)});_e.xlinkHref=new Ue("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){_e[e]=new Ue(e,1,!1,e.toLowerCase(),null,!0,!0)});function ro(e,t,n,r){var l=_e.hasOwnProperty(t)?_e[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(hf(t,n,l,r)&&(n=null),r||l===null?mf(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Tt=df.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,tl=Symbol.for("react.element"),En=Symbol.for("react.portal"),In=Symbol.for("react.fragment"),lo=Symbol.for("react.strict_mode"),sa=Symbol.for("react.profiler"),Tc=Symbol.for("react.provider"),Dc=Symbol.for("react.context"),so=Symbol.for("react.forward_ref"),aa=Symbol.for("react.suspense"),oa=Symbol.for("react.suspense_list"),ao=Symbol.for("react.memo"),Ft=Symbol.for("react.lazy"),Lc=Symbol.for("react.offscreen"),si=Symbol.iterator;function rr(e){return e===null||typeof e!="object"?null:(e=si&&e[si]||e["@@iterator"],typeof e=="function"?e:null)}var he=Object.assign,zs;function dr(e){if(zs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);zs=t&&t[1]||""}return`
`+zs+e}var Ms=!1;function _s(e,t){if(!e||Ms)return"";Ms=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var l=u.stack.split(`
`),a=r.stack.split(`
`),o=l.length-1,i=a.length-1;1<=o&&0<=i&&l[o]!==a[i];)i--;for(;1<=o&&0<=i;o--,i--)if(l[o]!==a[i]){if(o!==1||i!==1)do if(o--,i--,0>i||l[o]!==a[i]){var c=`
`+l[o].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=o&&0<=i);break}}}finally{Ms=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?dr(e):""}function gf(e){switch(e.tag){case 5:return dr(e.type);case 16:return dr("Lazy");case 13:return dr("Suspense");case 19:return dr("SuspenseList");case 0:case 2:case 15:return e=_s(e.type,!1),e;case 11:return e=_s(e.type.render,!1),e;case 1:return e=_s(e.type,!0),e;default:return""}}function ia(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case In:return"Fragment";case En:return"Portal";case sa:return"Profiler";case lo:return"StrictMode";case aa:return"Suspense";case oa:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Dc:return(e.displayName||"Context")+".Consumer";case Tc:return(e._context.displayName||"Context")+".Provider";case so:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case ao:return t=e.displayName||null,t!==null?t:ia(e.type)||"Memo";case Ft:t=e._payload,e=e._init;try{return ia(e(t))}catch{}}return null}function xf(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return ia(t);case 8:return t===lo?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Zt(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Fc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function yf(e){var t=Fc(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){r=""+o,a.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function nl(e){e._valueTracker||(e._valueTracker=yf(e))}function Rc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Fc(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function zl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ca(e,t){var n=t.checked;return he({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ai(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Zt(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Oc(e,t){t=t.checked,t!=null&&ro(e,"checked",t,!1)}function ua(e,t){Oc(e,t);var n=Zt(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?da(e,t.type,n):t.hasOwnProperty("defaultValue")&&da(e,t.type,Zt(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function oi(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function da(e,t,n){(t!=="number"||zl(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var fr=Array.isArray;function On(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Zt(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function fa(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(z(91));return he({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ii(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(z(92));if(fr(n)){if(1<n.length)throw Error(z(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Zt(n)}}function $c(e,t){var n=Zt(t.value),r=Zt(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ci(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Uc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ma(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Uc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var rl,Hc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(rl=rl||document.createElement("div"),rl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=rl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Sr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var hr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},vf=["Webkit","ms","Moz","O"];Object.keys(hr).forEach(function(e){vf.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),hr[t]=hr[e]})});function Bc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||hr.hasOwnProperty(e)&&hr[e]?(""+t).trim():t+"px"}function Vc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=Bc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var wf=he({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function pa(e,t){if(t){if(wf[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(z(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(z(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(z(61))}if(t.style!=null&&typeof t.style!="object")throw Error(z(62))}}function ha(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ga=null;function oo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var xa=null,$n=null,Un=null;function ui(e){if(e=Wr(e)){if(typeof xa!="function")throw Error(z(280));var t=e.stateNode;t&&(t=is(t),xa(e.stateNode,e.type,t))}}function Wc(e){$n?Un?Un.push(e):Un=[e]:$n=e}function Gc(){if($n){var e=$n,t=Un;if(Un=$n=null,ui(e),t)for(e=0;e<t.length;e++)ui(t[e])}}function Xc(e,t){return e(t)}function Qc(){}var As=!1;function Kc(e,t,n){if(As)return e(t,n);As=!0;try{return Xc(e,t,n)}finally{As=!1,($n!==null||Un!==null)&&(Qc(),Gc())}}function Er(e,t){var n=e.stateNode;if(n===null)return null;var r=is(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(z(231,t,typeof n));return n}var ya=!1;if(zt)try{var lr={};Object.defineProperty(lr,"passive",{get:function(){ya=!0}}),window.addEventListener("test",lr,lr),window.removeEventListener("test",lr,lr)}catch{ya=!1}function jf(e,t,n,r,l,a,o,i,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(x){this.onError(x)}}var gr=!1,Ml=null,_l=!1,va=null,Nf={onError:function(e){gr=!0,Ml=e}};function kf(e,t,n,r,l,a,o,i,c){gr=!1,Ml=null,jf.apply(Nf,arguments)}function bf(e,t,n,r,l,a,o,i,c){if(kf.apply(this,arguments),gr){if(gr){var u=Ml;gr=!1,Ml=null}else throw Error(z(198));_l||(_l=!0,va=u)}}function jn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Yc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function di(e){if(jn(e)!==e)throw Error(z(188))}function Cf(e){var t=e.alternate;if(!t){if(t=jn(e),t===null)throw Error(z(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var a=l.alternate;if(a===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===a.child){for(a=l.child;a;){if(a===n)return di(l),e;if(a===r)return di(l),t;a=a.sibling}throw Error(z(188))}if(n.return!==r.return)n=l,r=a;else{for(var o=!1,i=l.child;i;){if(i===n){o=!0,n=l,r=a;break}if(i===r){o=!0,r=l,n=a;break}i=i.sibling}if(!o){for(i=a.child;i;){if(i===n){o=!0,n=a,r=l;break}if(i===r){o=!0,r=a,n=l;break}i=i.sibling}if(!o)throw Error(z(189))}}if(n.alternate!==r)throw Error(z(190))}if(n.tag!==3)throw Error(z(188));return n.stateNode.current===n?e:t}function Zc(e){return e=Cf(e),e!==null?qc(e):null}function qc(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=qc(e);if(t!==null)return t;e=e.sibling}return null}var Jc=et.unstable_scheduleCallback,fi=et.unstable_cancelCallback,Sf=et.unstable_shouldYield,Ef=et.unstable_requestPaint,je=et.unstable_now,If=et.unstable_getCurrentPriorityLevel,io=et.unstable_ImmediatePriority,eu=et.unstable_UserBlockingPriority,Al=et.unstable_NormalPriority,Pf=et.unstable_LowPriority,tu=et.unstable_IdlePriority,ls=null,kt=null;function zf(e){if(kt&&typeof kt.onCommitFiberRoot=="function")try{kt.onCommitFiberRoot(ls,e,void 0,(e.current.flags&128)===128)}catch{}}var ht=Math.clz32?Math.clz32:Af,Mf=Math.log,_f=Math.LN2;function Af(e){return e>>>=0,e===0?32:31-(Mf(e)/_f|0)|0}var ll=64,sl=4194304;function mr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Tl(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,a=e.pingedLanes,o=n&268435455;if(o!==0){var i=o&~l;i!==0?r=mr(i):(a&=o,a!==0&&(r=mr(a)))}else o=n&~l,o!==0?r=mr(o):a!==0&&(r=mr(a));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,a=t&-t,l>=a||l===16&&(a&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ht(t),l=1<<n,r|=e[n],t&=~l;return r}function Tf(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Df(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,a=e.pendingLanes;0<a;){var o=31-ht(a),i=1<<o,c=l[o];c===-1?(!(i&n)||i&r)&&(l[o]=Tf(i,t)):c<=t&&(e.expiredLanes|=i),a&=~i}}function wa(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function nu(){var e=ll;return ll<<=1,!(ll&4194240)&&(ll=64),e}function Ts(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Br(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ht(t),e[t]=n}function Lf(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-ht(n),a=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~a}}function co(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ht(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var se=0;function ru(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var lu,uo,su,au,ou,ja=!1,al=[],Bt=null,Vt=null,Wt=null,Ir=new Map,Pr=new Map,Ot=[],Ff="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function mi(e,t){switch(e){case"focusin":case"focusout":Bt=null;break;case"dragenter":case"dragleave":Vt=null;break;case"mouseover":case"mouseout":Wt=null;break;case"pointerover":case"pointerout":Ir.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Pr.delete(t.pointerId)}}function sr(e,t,n,r,l,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[l]},t!==null&&(t=Wr(t),t!==null&&uo(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Rf(e,t,n,r,l){switch(t){case"focusin":return Bt=sr(Bt,e,t,n,r,l),!0;case"dragenter":return Vt=sr(Vt,e,t,n,r,l),!0;case"mouseover":return Wt=sr(Wt,e,t,n,r,l),!0;case"pointerover":var a=l.pointerId;return Ir.set(a,sr(Ir.get(a)||null,e,t,n,r,l)),!0;case"gotpointercapture":return a=l.pointerId,Pr.set(a,sr(Pr.get(a)||null,e,t,n,r,l)),!0}return!1}function iu(e){var t=on(e.target);if(t!==null){var n=jn(t);if(n!==null){if(t=n.tag,t===13){if(t=Yc(n),t!==null){e.blockedOn=t,ou(e.priority,function(){su(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function wl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Na(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);ga=r,n.target.dispatchEvent(r),ga=null}else return t=Wr(n),t!==null&&uo(t),e.blockedOn=n,!1;t.shift()}return!0}function pi(e,t,n){wl(e)&&n.delete(t)}function Of(){ja=!1,Bt!==null&&wl(Bt)&&(Bt=null),Vt!==null&&wl(Vt)&&(Vt=null),Wt!==null&&wl(Wt)&&(Wt=null),Ir.forEach(pi),Pr.forEach(pi)}function ar(e,t){e.blockedOn===t&&(e.blockedOn=null,ja||(ja=!0,et.unstable_scheduleCallback(et.unstable_NormalPriority,Of)))}function zr(e){function t(l){return ar(l,e)}if(0<al.length){ar(al[0],e);for(var n=1;n<al.length;n++){var r=al[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Bt!==null&&ar(Bt,e),Vt!==null&&ar(Vt,e),Wt!==null&&ar(Wt,e),Ir.forEach(t),Pr.forEach(t),n=0;n<Ot.length;n++)r=Ot[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ot.length&&(n=Ot[0],n.blockedOn===null);)iu(n),n.blockedOn===null&&Ot.shift()}var Hn=Tt.ReactCurrentBatchConfig,Dl=!0;function $f(e,t,n,r){var l=se,a=Hn.transition;Hn.transition=null;try{se=1,fo(e,t,n,r)}finally{se=l,Hn.transition=a}}function Uf(e,t,n,r){var l=se,a=Hn.transition;Hn.transition=null;try{se=4,fo(e,t,n,r)}finally{se=l,Hn.transition=a}}function fo(e,t,n,r){if(Dl){var l=Na(e,t,n,r);if(l===null)Vs(e,t,r,Ll,n),mi(e,r);else if(Rf(l,e,t,n,r))r.stopPropagation();else if(mi(e,r),t&4&&-1<Ff.indexOf(e)){for(;l!==null;){var a=Wr(l);if(a!==null&&lu(a),a=Na(e,t,n,r),a===null&&Vs(e,t,r,Ll,n),a===l)break;l=a}l!==null&&r.stopPropagation()}else Vs(e,t,r,null,n)}}var Ll=null;function Na(e,t,n,r){if(Ll=null,e=oo(r),e=on(e),e!==null)if(t=jn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Yc(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ll=e,null}function cu(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(If()){case io:return 1;case eu:return 4;case Al:case Pf:return 16;case tu:return 536870912;default:return 16}default:return 16}}var Ut=null,mo=null,jl=null;function uu(){if(jl)return jl;var e,t=mo,n=t.length,r,l="value"in Ut?Ut.value:Ut.textContent,a=l.length;for(e=0;e<n&&t[e]===l[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===l[a-r];r++);return jl=l.slice(e,1<r?1-r:void 0)}function Nl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ol(){return!0}function hi(){return!1}function nt(e){function t(n,r,l,a,o){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=a,this.target=o,this.currentTarget=null;for(var i in e)e.hasOwnProperty(i)&&(n=e[i],this[i]=n?n(a):a[i]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?ol:hi,this.isPropagationStopped=hi,this}return he(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ol)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ol)},persist:function(){},isPersistent:ol}),t}var er={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},po=nt(er),Vr=he({},er,{view:0,detail:0}),Hf=nt(Vr),Ds,Ls,or,ss=he({},Vr,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ho,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==or&&(or&&e.type==="mousemove"?(Ds=e.screenX-or.screenX,Ls=e.screenY-or.screenY):Ls=Ds=0,or=e),Ds)},movementY:function(e){return"movementY"in e?e.movementY:Ls}}),gi=nt(ss),Bf=he({},ss,{dataTransfer:0}),Vf=nt(Bf),Wf=he({},Vr,{relatedTarget:0}),Fs=nt(Wf),Gf=he({},er,{animationName:0,elapsedTime:0,pseudoElement:0}),Xf=nt(Gf),Qf=he({},er,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Kf=nt(Qf),Yf=he({},er,{data:0}),xi=nt(Yf),Zf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},qf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Jf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function em(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Jf[e])?!!t[e]:!1}function ho(){return em}var tm=he({},Vr,{key:function(e){if(e.key){var t=Zf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Nl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?qf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ho,charCode:function(e){return e.type==="keypress"?Nl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Nl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),nm=nt(tm),rm=he({},ss,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),yi=nt(rm),lm=he({},Vr,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ho}),sm=nt(lm),am=he({},er,{propertyName:0,elapsedTime:0,pseudoElement:0}),om=nt(am),im=he({},ss,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),cm=nt(im),um=[9,13,27,32],go=zt&&"CompositionEvent"in window,xr=null;zt&&"documentMode"in document&&(xr=document.documentMode);var dm=zt&&"TextEvent"in window&&!xr,du=zt&&(!go||xr&&8<xr&&11>=xr),vi=" ",wi=!1;function fu(e,t){switch(e){case"keyup":return um.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function mu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Pn=!1;function fm(e,t){switch(e){case"compositionend":return mu(t);case"keypress":return t.which!==32?null:(wi=!0,vi);case"textInput":return e=t.data,e===vi&&wi?null:e;default:return null}}function mm(e,t){if(Pn)return e==="compositionend"||!go&&fu(e,t)?(e=uu(),jl=mo=Ut=null,Pn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return du&&t.locale!=="ko"?null:t.data;default:return null}}var pm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ji(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!pm[e.type]:t==="textarea"}function pu(e,t,n,r){Wc(r),t=Fl(t,"onChange"),0<t.length&&(n=new po("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var yr=null,Mr=null;function hm(e){Cu(e,0)}function as(e){var t=_n(e);if(Rc(t))return e}function gm(e,t){if(e==="change")return t}var hu=!1;if(zt){var Rs;if(zt){var Os="oninput"in document;if(!Os){var Ni=document.createElement("div");Ni.setAttribute("oninput","return;"),Os=typeof Ni.oninput=="function"}Rs=Os}else Rs=!1;hu=Rs&&(!document.documentMode||9<document.documentMode)}function ki(){yr&&(yr.detachEvent("onpropertychange",gu),Mr=yr=null)}function gu(e){if(e.propertyName==="value"&&as(Mr)){var t=[];pu(t,Mr,e,oo(e)),Kc(hm,t)}}function xm(e,t,n){e==="focusin"?(ki(),yr=t,Mr=n,yr.attachEvent("onpropertychange",gu)):e==="focusout"&&ki()}function ym(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return as(Mr)}function vm(e,t){if(e==="click")return as(t)}function wm(e,t){if(e==="input"||e==="change")return as(t)}function jm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xt=typeof Object.is=="function"?Object.is:jm;function _r(e,t){if(xt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!la.call(t,l)||!xt(e[l],t[l]))return!1}return!0}function bi(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ci(e,t){var n=bi(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=bi(n)}}function xu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?xu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function yu(){for(var e=window,t=zl();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=zl(e.document)}return t}function xo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Nm(e){var t=yu(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&xu(n.ownerDocument.documentElement,n)){if(r!==null&&xo(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,a=Math.min(r.start,l);r=r.end===void 0?a:Math.min(r.end,l),!e.extend&&a>r&&(l=r,r=a,a=l),l=Ci(n,a);var o=Ci(n,r);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var km=zt&&"documentMode"in document&&11>=document.documentMode,zn=null,ka=null,vr=null,ba=!1;function Si(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;ba||zn==null||zn!==zl(r)||(r=zn,"selectionStart"in r&&xo(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),vr&&_r(vr,r)||(vr=r,r=Fl(ka,"onSelect"),0<r.length&&(t=new po("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=zn)))}function il(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Mn={animationend:il("Animation","AnimationEnd"),animationiteration:il("Animation","AnimationIteration"),animationstart:il("Animation","AnimationStart"),transitionend:il("Transition","TransitionEnd")},$s={},vu={};zt&&(vu=document.createElement("div").style,"AnimationEvent"in window||(delete Mn.animationend.animation,delete Mn.animationiteration.animation,delete Mn.animationstart.animation),"TransitionEvent"in window||delete Mn.transitionend.transition);function os(e){if($s[e])return $s[e];if(!Mn[e])return e;var t=Mn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in vu)return $s[e]=t[n];return e}var wu=os("animationend"),ju=os("animationiteration"),Nu=os("animationstart"),ku=os("transitionend"),bu=new Map,Ei="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Jt(e,t){bu.set(e,t),wn(t,[e])}for(var Us=0;Us<Ei.length;Us++){var Hs=Ei[Us],bm=Hs.toLowerCase(),Cm=Hs[0].toUpperCase()+Hs.slice(1);Jt(bm,"on"+Cm)}Jt(wu,"onAnimationEnd");Jt(ju,"onAnimationIteration");Jt(Nu,"onAnimationStart");Jt("dblclick","onDoubleClick");Jt("focusin","onFocus");Jt("focusout","onBlur");Jt(ku,"onTransitionEnd");Gn("onMouseEnter",["mouseout","mouseover"]);Gn("onMouseLeave",["mouseout","mouseover"]);Gn("onPointerEnter",["pointerout","pointerover"]);Gn("onPointerLeave",["pointerout","pointerover"]);wn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));wn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));wn("onBeforeInput",["compositionend","keypress","textInput","paste"]);wn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));wn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));wn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var pr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Sm=new Set("cancel close invalid load scroll toggle".split(" ").concat(pr));function Ii(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,bf(r,t,void 0,e),e.currentTarget=null}function Cu(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],c=i.instance,u=i.currentTarget;if(i=i.listener,c!==a&&l.isPropagationStopped())break e;Ii(l,i,u),a=c}else for(o=0;o<r.length;o++){if(i=r[o],c=i.instance,u=i.currentTarget,i=i.listener,c!==a&&l.isPropagationStopped())break e;Ii(l,i,u),a=c}}}if(_l)throw e=va,_l=!1,va=null,e}function ue(e,t){var n=t[Pa];n===void 0&&(n=t[Pa]=new Set);var r=e+"__bubble";n.has(r)||(Su(t,e,2,!1),n.add(r))}function Bs(e,t,n){var r=0;t&&(r|=4),Su(n,e,r,t)}var cl="_reactListening"+Math.random().toString(36).slice(2);function Ar(e){if(!e[cl]){e[cl]=!0,Ac.forEach(function(n){n!=="selectionchange"&&(Sm.has(n)||Bs(n,!1,e),Bs(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[cl]||(t[cl]=!0,Bs("selectionchange",!1,t))}}function Su(e,t,n,r){switch(cu(t)){case 1:var l=$f;break;case 4:l=Uf;break;default:l=fo}n=l.bind(null,t,n,e),l=void 0,!ya||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function Vs(e,t,n,r,l){var a=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var i=r.stateNode.containerInfo;if(i===l||i.nodeType===8&&i.parentNode===l)break;if(o===4)for(o=r.return;o!==null;){var c=o.tag;if((c===3||c===4)&&(c=o.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;o=o.return}for(;i!==null;){if(o=on(i),o===null)return;if(c=o.tag,c===5||c===6){r=a=o;continue e}i=i.parentNode}}r=r.return}Kc(function(){var u=a,x=oo(n),y=[];e:{var g=bu.get(e);if(g!==void 0){var j=po,w=e;switch(e){case"keypress":if(Nl(n)===0)break e;case"keydown":case"keyup":j=nm;break;case"focusin":w="focus",j=Fs;break;case"focusout":w="blur",j=Fs;break;case"beforeblur":case"afterblur":j=Fs;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":j=gi;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":j=Vf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":j=sm;break;case wu:case ju:case Nu:j=Xf;break;case ku:j=om;break;case"scroll":j=Hf;break;case"wheel":j=cm;break;case"copy":case"cut":case"paste":j=Kf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":j=yi}var k=(t&4)!==0,U=!k&&e==="scroll",f=k?g!==null?g+"Capture":null:g;k=[];for(var d=u,m;d!==null;){m=d;var p=m.stateNode;if(m.tag===5&&p!==null&&(m=p,f!==null&&(p=Er(d,f),p!=null&&k.push(Tr(d,p,m)))),U)break;d=d.return}0<k.length&&(g=new j(g,w,null,n,x),y.push({event:g,listeners:k}))}}if(!(t&7)){e:{if(g=e==="mouseover"||e==="pointerover",j=e==="mouseout"||e==="pointerout",g&&n!==ga&&(w=n.relatedTarget||n.fromElement)&&(on(w)||w[Mt]))break e;if((j||g)&&(g=x.window===x?x:(g=x.ownerDocument)?g.defaultView||g.parentWindow:window,j?(w=n.relatedTarget||n.toElement,j=u,w=w?on(w):null,w!==null&&(U=jn(w),w!==U||w.tag!==5&&w.tag!==6)&&(w=null)):(j=null,w=u),j!==w)){if(k=gi,p="onMouseLeave",f="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(k=yi,p="onPointerLeave",f="onPointerEnter",d="pointer"),U=j==null?g:_n(j),m=w==null?g:_n(w),g=new k(p,d+"leave",j,n,x),g.target=U,g.relatedTarget=m,p=null,on(x)===u&&(k=new k(f,d+"enter",w,n,x),k.target=m,k.relatedTarget=U,p=k),U=p,j&&w)t:{for(k=j,f=w,d=0,m=k;m;m=Cn(m))d++;for(m=0,p=f;p;p=Cn(p))m++;for(;0<d-m;)k=Cn(k),d--;for(;0<m-d;)f=Cn(f),m--;for(;d--;){if(k===f||f!==null&&k===f.alternate)break t;k=Cn(k),f=Cn(f)}k=null}else k=null;j!==null&&Pi(y,g,j,k,!1),w!==null&&U!==null&&Pi(y,U,w,k,!0)}}e:{if(g=u?_n(u):window,j=g.nodeName&&g.nodeName.toLowerCase(),j==="select"||j==="input"&&g.type==="file")var b=gm;else if(ji(g))if(hu)b=wm;else{b=ym;var M=xm}else(j=g.nodeName)&&j.toLowerCase()==="input"&&(g.type==="checkbox"||g.type==="radio")&&(b=vm);if(b&&(b=b(e,u))){pu(y,b,n,x);break e}M&&M(e,g,u),e==="focusout"&&(M=g._wrapperState)&&M.controlled&&g.type==="number"&&da(g,"number",g.value)}switch(M=u?_n(u):window,e){case"focusin":(ji(M)||M.contentEditable==="true")&&(zn=M,ka=u,vr=null);break;case"focusout":vr=ka=zn=null;break;case"mousedown":ba=!0;break;case"contextmenu":case"mouseup":case"dragend":ba=!1,Si(y,n,x);break;case"selectionchange":if(km)break;case"keydown":case"keyup":Si(y,n,x)}var F;if(go)e:{switch(e){case"compositionstart":var E="onCompositionStart";break e;case"compositionend":E="onCompositionEnd";break e;case"compositionupdate":E="onCompositionUpdate";break e}E=void 0}else Pn?fu(e,n)&&(E="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(E="onCompositionStart");E&&(du&&n.locale!=="ko"&&(Pn||E!=="onCompositionStart"?E==="onCompositionEnd"&&Pn&&(F=uu()):(Ut=x,mo="value"in Ut?Ut.value:Ut.textContent,Pn=!0)),M=Fl(u,E),0<M.length&&(E=new xi(E,e,null,n,x),y.push({event:E,listeners:M}),F?E.data=F:(F=mu(n),F!==null&&(E.data=F)))),(F=dm?fm(e,n):mm(e,n))&&(u=Fl(u,"onBeforeInput"),0<u.length&&(x=new xi("onBeforeInput","beforeinput",null,n,x),y.push({event:x,listeners:u}),x.data=F))}Cu(y,t)})}function Tr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Fl(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,a=l.stateNode;l.tag===5&&a!==null&&(l=a,a=Er(e,n),a!=null&&r.unshift(Tr(e,a,l)),a=Er(e,t),a!=null&&r.push(Tr(e,a,l))),e=e.return}return r}function Cn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Pi(e,t,n,r,l){for(var a=t._reactName,o=[];n!==null&&n!==r;){var i=n,c=i.alternate,u=i.stateNode;if(c!==null&&c===r)break;i.tag===5&&u!==null&&(i=u,l?(c=Er(n,a),c!=null&&o.unshift(Tr(n,c,i))):l||(c=Er(n,a),c!=null&&o.push(Tr(n,c,i)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Em=/\r\n?/g,Im=/\u0000|\uFFFD/g;function zi(e){return(typeof e=="string"?e:""+e).replace(Em,`
`).replace(Im,"")}function ul(e,t,n){if(t=zi(t),zi(e)!==t&&n)throw Error(z(425))}function Rl(){}var Ca=null,Sa=null;function Ea(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ia=typeof setTimeout=="function"?setTimeout:void 0,Pm=typeof clearTimeout=="function"?clearTimeout:void 0,Mi=typeof Promise=="function"?Promise:void 0,zm=typeof queueMicrotask=="function"?queueMicrotask:typeof Mi<"u"?function(e){return Mi.resolve(null).then(e).catch(Mm)}:Ia;function Mm(e){setTimeout(function(){throw e})}function Ws(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),zr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);zr(t)}function Gt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function _i(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var tr=Math.random().toString(36).slice(2),Nt="__reactFiber$"+tr,Dr="__reactProps$"+tr,Mt="__reactContainer$"+tr,Pa="__reactEvents$"+tr,_m="__reactListeners$"+tr,Am="__reactHandles$"+tr;function on(e){var t=e[Nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Mt]||n[Nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=_i(e);e!==null;){if(n=e[Nt])return n;e=_i(e)}return t}e=n,n=e.parentNode}return null}function Wr(e){return e=e[Nt]||e[Mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function _n(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(z(33))}function is(e){return e[Dr]||null}var za=[],An=-1;function en(e){return{current:e}}function de(e){0>An||(e.current=za[An],za[An]=null,An--)}function ce(e,t){An++,za[An]=e.current,e.current=t}var qt={},Le=en(qt),Xe=en(!1),pn=qt;function Xn(e,t){var n=e.type.contextTypes;if(!n)return qt;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},a;for(a in n)l[a]=t[a];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Qe(e){return e=e.childContextTypes,e!=null}function Ol(){de(Xe),de(Le)}function Ai(e,t,n){if(Le.current!==qt)throw Error(z(168));ce(Le,t),ce(Xe,n)}function Eu(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(z(108,xf(e)||"Unknown",l));return he({},n,r)}function $l(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||qt,pn=Le.current,ce(Le,e),ce(Xe,Xe.current),!0}function Ti(e,t,n){var r=e.stateNode;if(!r)throw Error(z(169));n?(e=Eu(e,t,pn),r.__reactInternalMemoizedMergedChildContext=e,de(Xe),de(Le),ce(Le,e)):de(Xe),ce(Xe,n)}var St=null,cs=!1,Gs=!1;function Iu(e){St===null?St=[e]:St.push(e)}function Tm(e){cs=!0,Iu(e)}function tn(){if(!Gs&&St!==null){Gs=!0;var e=0,t=se;try{var n=St;for(se=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}St=null,cs=!1}catch(l){throw St!==null&&(St=St.slice(e+1)),Jc(io,tn),l}finally{se=t,Gs=!1}}return null}var Tn=[],Dn=0,Ul=null,Hl=0,rt=[],lt=0,hn=null,Et=1,It="";function sn(e,t){Tn[Dn++]=Hl,Tn[Dn++]=Ul,Ul=e,Hl=t}function Pu(e,t,n){rt[lt++]=Et,rt[lt++]=It,rt[lt++]=hn,hn=e;var r=Et;e=It;var l=32-ht(r)-1;r&=~(1<<l),n+=1;var a=32-ht(t)+l;if(30<a){var o=l-l%5;a=(r&(1<<o)-1).toString(32),r>>=o,l-=o,Et=1<<32-ht(t)+l|n<<l|r,It=a+e}else Et=1<<a|n<<l|r,It=e}function yo(e){e.return!==null&&(sn(e,1),Pu(e,1,0))}function vo(e){for(;e===Ul;)Ul=Tn[--Dn],Tn[Dn]=null,Hl=Tn[--Dn],Tn[Dn]=null;for(;e===hn;)hn=rt[--lt],rt[lt]=null,It=rt[--lt],rt[lt]=null,Et=rt[--lt],rt[lt]=null}var Je=null,qe=null,fe=!1,pt=null;function zu(e,t){var n=st(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Di(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Je=e,qe=Gt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Je=e,qe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=hn!==null?{id:Et,overflow:It}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=st(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Je=e,qe=null,!0):!1;default:return!1}}function Ma(e){return(e.mode&1)!==0&&(e.flags&128)===0}function _a(e){if(fe){var t=qe;if(t){var n=t;if(!Di(e,t)){if(Ma(e))throw Error(z(418));t=Gt(n.nextSibling);var r=Je;t&&Di(e,t)?zu(r,n):(e.flags=e.flags&-4097|2,fe=!1,Je=e)}}else{if(Ma(e))throw Error(z(418));e.flags=e.flags&-4097|2,fe=!1,Je=e}}}function Li(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Je=e}function dl(e){if(e!==Je)return!1;if(!fe)return Li(e),fe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ea(e.type,e.memoizedProps)),t&&(t=qe)){if(Ma(e))throw Mu(),Error(z(418));for(;t;)zu(e,t),t=Gt(t.nextSibling)}if(Li(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(z(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){qe=Gt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}qe=null}}else qe=Je?Gt(e.stateNode.nextSibling):null;return!0}function Mu(){for(var e=qe;e;)e=Gt(e.nextSibling)}function Qn(){qe=Je=null,fe=!1}function wo(e){pt===null?pt=[e]:pt.push(e)}var Dm=Tt.ReactCurrentBatchConfig;function ir(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(z(309));var r=n.stateNode}if(!r)throw Error(z(147,e));var l=r,a=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===a?t.ref:(t=function(o){var i=l.refs;o===null?delete i[a]:i[a]=o},t._stringRef=a,t)}if(typeof e!="string")throw Error(z(284));if(!n._owner)throw Error(z(290,e))}return e}function fl(e,t){throw e=Object.prototype.toString.call(t),Error(z(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Fi(e){var t=e._init;return t(e._payload)}function _u(e){function t(f,d){if(e){var m=f.deletions;m===null?(f.deletions=[d],f.flags|=16):m.push(d)}}function n(f,d){if(!e)return null;for(;d!==null;)t(f,d),d=d.sibling;return null}function r(f,d){for(f=new Map;d!==null;)d.key!==null?f.set(d.key,d):f.set(d.index,d),d=d.sibling;return f}function l(f,d){return f=Yt(f,d),f.index=0,f.sibling=null,f}function a(f,d,m){return f.index=m,e?(m=f.alternate,m!==null?(m=m.index,m<d?(f.flags|=2,d):m):(f.flags|=2,d)):(f.flags|=1048576,d)}function o(f){return e&&f.alternate===null&&(f.flags|=2),f}function i(f,d,m,p){return d===null||d.tag!==6?(d=Js(m,f.mode,p),d.return=f,d):(d=l(d,m),d.return=f,d)}function c(f,d,m,p){var b=m.type;return b===In?x(f,d,m.props.children,p,m.key):d!==null&&(d.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===Ft&&Fi(b)===d.type)?(p=l(d,m.props),p.ref=ir(f,d,m),p.return=f,p):(p=Pl(m.type,m.key,m.props,null,f.mode,p),p.ref=ir(f,d,m),p.return=f,p)}function u(f,d,m,p){return d===null||d.tag!==4||d.stateNode.containerInfo!==m.containerInfo||d.stateNode.implementation!==m.implementation?(d=ea(m,f.mode,p),d.return=f,d):(d=l(d,m.children||[]),d.return=f,d)}function x(f,d,m,p,b){return d===null||d.tag!==7?(d=fn(m,f.mode,p,b),d.return=f,d):(d=l(d,m),d.return=f,d)}function y(f,d,m){if(typeof d=="string"&&d!==""||typeof d=="number")return d=Js(""+d,f.mode,m),d.return=f,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case tl:return m=Pl(d.type,d.key,d.props,null,f.mode,m),m.ref=ir(f,null,d),m.return=f,m;case En:return d=ea(d,f.mode,m),d.return=f,d;case Ft:var p=d._init;return y(f,p(d._payload),m)}if(fr(d)||rr(d))return d=fn(d,f.mode,m,null),d.return=f,d;fl(f,d)}return null}function g(f,d,m,p){var b=d!==null?d.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return b!==null?null:i(f,d,""+m,p);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case tl:return m.key===b?c(f,d,m,p):null;case En:return m.key===b?u(f,d,m,p):null;case Ft:return b=m._init,g(f,d,b(m._payload),p)}if(fr(m)||rr(m))return b!==null?null:x(f,d,m,p,null);fl(f,m)}return null}function j(f,d,m,p,b){if(typeof p=="string"&&p!==""||typeof p=="number")return f=f.get(m)||null,i(d,f,""+p,b);if(typeof p=="object"&&p!==null){switch(p.$$typeof){case tl:return f=f.get(p.key===null?m:p.key)||null,c(d,f,p,b);case En:return f=f.get(p.key===null?m:p.key)||null,u(d,f,p,b);case Ft:var M=p._init;return j(f,d,m,M(p._payload),b)}if(fr(p)||rr(p))return f=f.get(m)||null,x(d,f,p,b,null);fl(d,p)}return null}function w(f,d,m,p){for(var b=null,M=null,F=d,E=d=0,B=null;F!==null&&E<m.length;E++){F.index>E?(B=F,F=null):B=F.sibling;var L=g(f,F,m[E],p);if(L===null){F===null&&(F=B);break}e&&F&&L.alternate===null&&t(f,F),d=a(L,d,E),M===null?b=L:M.sibling=L,M=L,F=B}if(E===m.length)return n(f,F),fe&&sn(f,E),b;if(F===null){for(;E<m.length;E++)F=y(f,m[E],p),F!==null&&(d=a(F,d,E),M===null?b=F:M.sibling=F,M=F);return fe&&sn(f,E),b}for(F=r(f,F);E<m.length;E++)B=j(F,f,E,m[E],p),B!==null&&(e&&B.alternate!==null&&F.delete(B.key===null?E:B.key),d=a(B,d,E),M===null?b=B:M.sibling=B,M=B);return e&&F.forEach(function(q){return t(f,q)}),fe&&sn(f,E),b}function k(f,d,m,p){var b=rr(m);if(typeof b!="function")throw Error(z(150));if(m=b.call(m),m==null)throw Error(z(151));for(var M=b=null,F=d,E=d=0,B=null,L=m.next();F!==null&&!L.done;E++,L=m.next()){F.index>E?(B=F,F=null):B=F.sibling;var q=g(f,F,L.value,p);if(q===null){F===null&&(F=B);break}e&&F&&q.alternate===null&&t(f,F),d=a(q,d,E),M===null?b=q:M.sibling=q,M=q,F=B}if(L.done)return n(f,F),fe&&sn(f,E),b;if(F===null){for(;!L.done;E++,L=m.next())L=y(f,L.value,p),L!==null&&(d=a(L,d,E),M===null?b=L:M.sibling=L,M=L);return fe&&sn(f,E),b}for(F=r(f,F);!L.done;E++,L=m.next())L=j(F,f,E,L.value,p),L!==null&&(e&&L.alternate!==null&&F.delete(L.key===null?E:L.key),d=a(L,d,E),M===null?b=L:M.sibling=L,M=L);return e&&F.forEach(function(ve){return t(f,ve)}),fe&&sn(f,E),b}function U(f,d,m,p){if(typeof m=="object"&&m!==null&&m.type===In&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case tl:e:{for(var b=m.key,M=d;M!==null;){if(M.key===b){if(b=m.type,b===In){if(M.tag===7){n(f,M.sibling),d=l(M,m.props.children),d.return=f,f=d;break e}}else if(M.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===Ft&&Fi(b)===M.type){n(f,M.sibling),d=l(M,m.props),d.ref=ir(f,M,m),d.return=f,f=d;break e}n(f,M);break}else t(f,M);M=M.sibling}m.type===In?(d=fn(m.props.children,f.mode,p,m.key),d.return=f,f=d):(p=Pl(m.type,m.key,m.props,null,f.mode,p),p.ref=ir(f,d,m),p.return=f,f=p)}return o(f);case En:e:{for(M=m.key;d!==null;){if(d.key===M)if(d.tag===4&&d.stateNode.containerInfo===m.containerInfo&&d.stateNode.implementation===m.implementation){n(f,d.sibling),d=l(d,m.children||[]),d.return=f,f=d;break e}else{n(f,d);break}else t(f,d);d=d.sibling}d=ea(m,f.mode,p),d.return=f,f=d}return o(f);case Ft:return M=m._init,U(f,d,M(m._payload),p)}if(fr(m))return w(f,d,m,p);if(rr(m))return k(f,d,m,p);fl(f,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,d!==null&&d.tag===6?(n(f,d.sibling),d=l(d,m),d.return=f,f=d):(n(f,d),d=Js(m,f.mode,p),d.return=f,f=d),o(f)):n(f,d)}return U}var Kn=_u(!0),Au=_u(!1),Bl=en(null),Vl=null,Ln=null,jo=null;function No(){jo=Ln=Vl=null}function ko(e){var t=Bl.current;de(Bl),e._currentValue=t}function Aa(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Bn(e,t){Vl=e,jo=Ln=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ge=!0),e.firstContext=null)}function ot(e){var t=e._currentValue;if(jo!==e)if(e={context:e,memoizedValue:t,next:null},Ln===null){if(Vl===null)throw Error(z(308));Ln=e,Vl.dependencies={lanes:0,firstContext:e}}else Ln=Ln.next=e;return t}var cn=null;function bo(e){cn===null?cn=[e]:cn.push(e)}function Tu(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,bo(t)):(n.next=l.next,l.next=n),t.interleaved=n,_t(e,r)}function _t(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Rt=!1;function Co(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Du(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Pt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Xt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,le&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,_t(e,n)}return l=r.interleaved,l===null?(t.next=t,bo(r)):(t.next=l.next,l.next=t),r.interleaved=t,_t(e,n)}function kl(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,co(e,n)}}function Ri(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,a=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};a===null?l=a=o:a=a.next=o,n=n.next}while(n!==null);a===null?l=a=t:a=a.next=t}else l=a=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:a,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wl(e,t,n,r){var l=e.updateQueue;Rt=!1;var a=l.firstBaseUpdate,o=l.lastBaseUpdate,i=l.shared.pending;if(i!==null){l.shared.pending=null;var c=i,u=c.next;c.next=null,o===null?a=u:o.next=u,o=c;var x=e.alternate;x!==null&&(x=x.updateQueue,i=x.lastBaseUpdate,i!==o&&(i===null?x.firstBaseUpdate=u:i.next=u,x.lastBaseUpdate=c))}if(a!==null){var y=l.baseState;o=0,x=u=c=null,i=a;do{var g=i.lane,j=i.eventTime;if((r&g)===g){x!==null&&(x=x.next={eventTime:j,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var w=e,k=i;switch(g=t,j=n,k.tag){case 1:if(w=k.payload,typeof w=="function"){y=w.call(j,y,g);break e}y=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=k.payload,g=typeof w=="function"?w.call(j,y,g):w,g==null)break e;y=he({},y,g);break e;case 2:Rt=!0}}i.callback!==null&&i.lane!==0&&(e.flags|=64,g=l.effects,g===null?l.effects=[i]:g.push(i))}else j={eventTime:j,lane:g,tag:i.tag,payload:i.payload,callback:i.callback,next:null},x===null?(u=x=j,c=y):x=x.next=j,o|=g;if(i=i.next,i===null){if(i=l.shared.pending,i===null)break;g=i,i=g.next,g.next=null,l.lastBaseUpdate=g,l.shared.pending=null}}while(!0);if(x===null&&(c=y),l.baseState=c,l.firstBaseUpdate=u,l.lastBaseUpdate=x,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else a===null&&(l.shared.lanes=0);xn|=o,e.lanes=o,e.memoizedState=y}}function Oi(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(z(191,l));l.call(r)}}}var Gr={},bt=en(Gr),Lr=en(Gr),Fr=en(Gr);function un(e){if(e===Gr)throw Error(z(174));return e}function So(e,t){switch(ce(Fr,t),ce(Lr,e),ce(bt,Gr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ma(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=ma(t,e)}de(bt),ce(bt,t)}function Yn(){de(bt),de(Lr),de(Fr)}function Lu(e){un(Fr.current);var t=un(bt.current),n=ma(t,e.type);t!==n&&(ce(Lr,e),ce(bt,n))}function Eo(e){Lr.current===e&&(de(bt),de(Lr))}var me=en(0);function Gl(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Xs=[];function Io(){for(var e=0;e<Xs.length;e++)Xs[e]._workInProgressVersionPrimary=null;Xs.length=0}var bl=Tt.ReactCurrentDispatcher,Qs=Tt.ReactCurrentBatchConfig,gn=0,pe=null,Se=null,Ie=null,Xl=!1,wr=!1,Rr=0,Lm=0;function Ae(){throw Error(z(321))}function Po(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!xt(e[n],t[n]))return!1;return!0}function zo(e,t,n,r,l,a){if(gn=a,pe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,bl.current=e===null||e.memoizedState===null?$m:Um,e=n(r,l),wr){a=0;do{if(wr=!1,Rr=0,25<=a)throw Error(z(301));a+=1,Ie=Se=null,t.updateQueue=null,bl.current=Hm,e=n(r,l)}while(wr)}if(bl.current=Ql,t=Se!==null&&Se.next!==null,gn=0,Ie=Se=pe=null,Xl=!1,t)throw Error(z(300));return e}function Mo(){var e=Rr!==0;return Rr=0,e}function jt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ie===null?pe.memoizedState=Ie=e:Ie=Ie.next=e,Ie}function it(){if(Se===null){var e=pe.alternate;e=e!==null?e.memoizedState:null}else e=Se.next;var t=Ie===null?pe.memoizedState:Ie.next;if(t!==null)Ie=t,Se=e;else{if(e===null)throw Error(z(310));Se=e,e={memoizedState:Se.memoizedState,baseState:Se.baseState,baseQueue:Se.baseQueue,queue:Se.queue,next:null},Ie===null?pe.memoizedState=Ie=e:Ie=Ie.next=e}return Ie}function Or(e,t){return typeof t=="function"?t(e):t}function Ks(e){var t=it(),n=t.queue;if(n===null)throw Error(z(311));n.lastRenderedReducer=e;var r=Se,l=r.baseQueue,a=n.pending;if(a!==null){if(l!==null){var o=l.next;l.next=a.next,a.next=o}r.baseQueue=l=a,n.pending=null}if(l!==null){a=l.next,r=r.baseState;var i=o=null,c=null,u=a;do{var x=u.lane;if((gn&x)===x)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var y={lane:x,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(i=c=y,o=r):c=c.next=y,pe.lanes|=x,xn|=x}u=u.next}while(u!==null&&u!==a);c===null?o=r:c.next=i,xt(r,t.memoizedState)||(Ge=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do a=l.lane,pe.lanes|=a,xn|=a,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ys(e){var t=it(),n=t.queue;if(n===null)throw Error(z(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,a=t.memoizedState;if(l!==null){n.pending=null;var o=l=l.next;do a=e(a,o.action),o=o.next;while(o!==l);xt(a,t.memoizedState)||(Ge=!0),t.memoizedState=a,t.baseQueue===null&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Fu(){}function Ru(e,t){var n=pe,r=it(),l=t(),a=!xt(r.memoizedState,l);if(a&&(r.memoizedState=l,Ge=!0),r=r.queue,_o(Uu.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||Ie!==null&&Ie.memoizedState.tag&1){if(n.flags|=2048,$r(9,$u.bind(null,n,r,l,t),void 0,null),Pe===null)throw Error(z(349));gn&30||Ou(n,t,l)}return l}function Ou(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function $u(e,t,n,r){t.value=n,t.getSnapshot=r,Hu(t)&&Bu(e)}function Uu(e,t,n){return n(function(){Hu(t)&&Bu(e)})}function Hu(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!xt(e,n)}catch{return!0}}function Bu(e){var t=_t(e,1);t!==null&&gt(t,e,1,-1)}function $i(e){var t=jt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Or,lastRenderedState:e},t.queue=e,e=e.dispatch=Om.bind(null,pe,e),[t.memoizedState,e]}function $r(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=pe.updateQueue,t===null?(t={lastEffect:null,stores:null},pe.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Vu(){return it().memoizedState}function Cl(e,t,n,r){var l=jt();pe.flags|=e,l.memoizedState=$r(1|t,n,void 0,r===void 0?null:r)}function us(e,t,n,r){var l=it();r=r===void 0?null:r;var a=void 0;if(Se!==null){var o=Se.memoizedState;if(a=o.destroy,r!==null&&Po(r,o.deps)){l.memoizedState=$r(t,n,a,r);return}}pe.flags|=e,l.memoizedState=$r(1|t,n,a,r)}function Ui(e,t){return Cl(8390656,8,e,t)}function _o(e,t){return us(2048,8,e,t)}function Wu(e,t){return us(4,2,e,t)}function Gu(e,t){return us(4,4,e,t)}function Xu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Qu(e,t,n){return n=n!=null?n.concat([e]):null,us(4,4,Xu.bind(null,t,e),n)}function Ao(){}function Ku(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Po(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Yu(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Po(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Zu(e,t,n){return gn&21?(xt(n,t)||(n=nu(),pe.lanes|=n,xn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ge=!0),e.memoizedState=n)}function Fm(e,t){var n=se;se=n!==0&&4>n?n:4,e(!0);var r=Qs.transition;Qs.transition={};try{e(!1),t()}finally{se=n,Qs.transition=r}}function qu(){return it().memoizedState}function Rm(e,t,n){var r=Kt(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ju(e))ed(t,n);else if(n=Tu(e,t,n,r),n!==null){var l=Oe();gt(n,e,r,l),td(n,t,r)}}function Om(e,t,n){var r=Kt(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ju(e))ed(t,l);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var o=t.lastRenderedState,i=a(o,n);if(l.hasEagerState=!0,l.eagerState=i,xt(i,o)){var c=t.interleaved;c===null?(l.next=l,bo(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}n=Tu(e,t,l,r),n!==null&&(l=Oe(),gt(n,e,r,l),td(n,t,r))}}function Ju(e){var t=e.alternate;return e===pe||t!==null&&t===pe}function ed(e,t){wr=Xl=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function td(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,co(e,n)}}var Ql={readContext:ot,useCallback:Ae,useContext:Ae,useEffect:Ae,useImperativeHandle:Ae,useInsertionEffect:Ae,useLayoutEffect:Ae,useMemo:Ae,useReducer:Ae,useRef:Ae,useState:Ae,useDebugValue:Ae,useDeferredValue:Ae,useTransition:Ae,useMutableSource:Ae,useSyncExternalStore:Ae,useId:Ae,unstable_isNewReconciler:!1},$m={readContext:ot,useCallback:function(e,t){return jt().memoizedState=[e,t===void 0?null:t],e},useContext:ot,useEffect:Ui,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Cl(4194308,4,Xu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Cl(4194308,4,e,t)},useInsertionEffect:function(e,t){return Cl(4,2,e,t)},useMemo:function(e,t){var n=jt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=jt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Rm.bind(null,pe,e),[r.memoizedState,e]},useRef:function(e){var t=jt();return e={current:e},t.memoizedState=e},useState:$i,useDebugValue:Ao,useDeferredValue:function(e){return jt().memoizedState=e},useTransition:function(){var e=$i(!1),t=e[0];return e=Fm.bind(null,e[1]),jt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=pe,l=jt();if(fe){if(n===void 0)throw Error(z(407));n=n()}else{if(n=t(),Pe===null)throw Error(z(349));gn&30||Ou(r,t,n)}l.memoizedState=n;var a={value:n,getSnapshot:t};return l.queue=a,Ui(Uu.bind(null,r,a,e),[e]),r.flags|=2048,$r(9,$u.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=jt(),t=Pe.identifierPrefix;if(fe){var n=It,r=Et;n=(r&~(1<<32-ht(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Rr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Lm++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Um={readContext:ot,useCallback:Ku,useContext:ot,useEffect:_o,useImperativeHandle:Qu,useInsertionEffect:Wu,useLayoutEffect:Gu,useMemo:Yu,useReducer:Ks,useRef:Vu,useState:function(){return Ks(Or)},useDebugValue:Ao,useDeferredValue:function(e){var t=it();return Zu(t,Se.memoizedState,e)},useTransition:function(){var e=Ks(Or)[0],t=it().memoizedState;return[e,t]},useMutableSource:Fu,useSyncExternalStore:Ru,useId:qu,unstable_isNewReconciler:!1},Hm={readContext:ot,useCallback:Ku,useContext:ot,useEffect:_o,useImperativeHandle:Qu,useInsertionEffect:Wu,useLayoutEffect:Gu,useMemo:Yu,useReducer:Ys,useRef:Vu,useState:function(){return Ys(Or)},useDebugValue:Ao,useDeferredValue:function(e){var t=it();return Se===null?t.memoizedState=e:Zu(t,Se.memoizedState,e)},useTransition:function(){var e=Ys(Or)[0],t=it().memoizedState;return[e,t]},useMutableSource:Fu,useSyncExternalStore:Ru,useId:qu,unstable_isNewReconciler:!1};function ft(e,t){if(e&&e.defaultProps){t=he({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ta(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:he({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var ds={isMounted:function(e){return(e=e._reactInternals)?jn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Oe(),l=Kt(e),a=Pt(r,l);a.payload=t,n!=null&&(a.callback=n),t=Xt(e,a,l),t!==null&&(gt(t,e,l,r),kl(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Oe(),l=Kt(e),a=Pt(r,l);a.tag=1,a.payload=t,n!=null&&(a.callback=n),t=Xt(e,a,l),t!==null&&(gt(t,e,l,r),kl(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Oe(),r=Kt(e),l=Pt(n,r);l.tag=2,t!=null&&(l.callback=t),t=Xt(e,l,r),t!==null&&(gt(t,e,r,n),kl(t,e,r))}};function Hi(e,t,n,r,l,a,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,a,o):t.prototype&&t.prototype.isPureReactComponent?!_r(n,r)||!_r(l,a):!0}function nd(e,t,n){var r=!1,l=qt,a=t.contextType;return typeof a=="object"&&a!==null?a=ot(a):(l=Qe(t)?pn:Le.current,r=t.contextTypes,a=(r=r!=null)?Xn(e,l):qt),t=new t(n,a),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=ds,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=a),t}function Bi(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ds.enqueueReplaceState(t,t.state,null)}function Da(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Co(e);var a=t.contextType;typeof a=="object"&&a!==null?l.context=ot(a):(a=Qe(t)?pn:Le.current,l.context=Xn(e,a)),l.state=e.memoizedState,a=t.getDerivedStateFromProps,typeof a=="function"&&(Ta(e,t,a,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&ds.enqueueReplaceState(l,l.state,null),Wl(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function Zn(e,t){try{var n="",r=t;do n+=gf(r),r=r.return;while(r);var l=n}catch(a){l=`
Error generating stack: `+a.message+`
`+a.stack}return{value:e,source:t,stack:l,digest:null}}function Zs(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function La(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Bm=typeof WeakMap=="function"?WeakMap:Map;function rd(e,t,n){n=Pt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Yl||(Yl=!0,Ga=r),La(e,t)},n}function ld(e,t,n){n=Pt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){La(e,t)}}var a=e.stateNode;return a!==null&&typeof a.componentDidCatch=="function"&&(n.callback=function(){La(e,t),typeof r!="function"&&(Qt===null?Qt=new Set([this]):Qt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Vi(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Bm;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=rp.bind(null,e,t,n),t.then(e,e))}function Wi(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Gi(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Pt(-1,1),t.tag=2,Xt(n,t,1))),n.lanes|=1),e)}var Vm=Tt.ReactCurrentOwner,Ge=!1;function Re(e,t,n,r){t.child=e===null?Au(t,null,n,r):Kn(t,e.child,n,r)}function Xi(e,t,n,r,l){n=n.render;var a=t.ref;return Bn(t,l),r=zo(e,t,n,r,a,l),n=Mo(),e!==null&&!Ge?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,At(e,t,l)):(fe&&n&&yo(t),t.flags|=1,Re(e,t,r,l),t.child)}function Qi(e,t,n,r,l){if(e===null){var a=n.type;return typeof a=="function"&&!Uo(a)&&a.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=a,sd(e,t,a,r,l)):(e=Pl(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,!(e.lanes&l)){var o=a.memoizedProps;if(n=n.compare,n=n!==null?n:_r,n(o,r)&&e.ref===t.ref)return At(e,t,l)}return t.flags|=1,e=Yt(a,r),e.ref=t.ref,e.return=t,t.child=e}function sd(e,t,n,r,l){if(e!==null){var a=e.memoizedProps;if(_r(a,r)&&e.ref===t.ref)if(Ge=!1,t.pendingProps=r=a,(e.lanes&l)!==0)e.flags&131072&&(Ge=!0);else return t.lanes=e.lanes,At(e,t,l)}return Fa(e,t,n,r,l)}function ad(e,t,n){var r=t.pendingProps,l=r.children,a=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ce(Rn,Ze),Ze|=n;else{if(!(n&1073741824))return e=a!==null?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ce(Rn,Ze),Ze|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=a!==null?a.baseLanes:n,ce(Rn,Ze),Ze|=r}else a!==null?(r=a.baseLanes|n,t.memoizedState=null):r=n,ce(Rn,Ze),Ze|=r;return Re(e,t,l,n),t.child}function od(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Fa(e,t,n,r,l){var a=Qe(n)?pn:Le.current;return a=Xn(t,a),Bn(t,l),n=zo(e,t,n,r,a,l),r=Mo(),e!==null&&!Ge?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,At(e,t,l)):(fe&&r&&yo(t),t.flags|=1,Re(e,t,n,l),t.child)}function Ki(e,t,n,r,l){if(Qe(n)){var a=!0;$l(t)}else a=!1;if(Bn(t,l),t.stateNode===null)Sl(e,t),nd(t,n,r),Da(t,n,r,l),r=!0;else if(e===null){var o=t.stateNode,i=t.memoizedProps;o.props=i;var c=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=ot(u):(u=Qe(n)?pn:Le.current,u=Xn(t,u));var x=n.getDerivedStateFromProps,y=typeof x=="function"||typeof o.getSnapshotBeforeUpdate=="function";y||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==r||c!==u)&&Bi(t,o,r,u),Rt=!1;var g=t.memoizedState;o.state=g,Wl(t,r,o,l),c=t.memoizedState,i!==r||g!==c||Xe.current||Rt?(typeof x=="function"&&(Ta(t,n,x,r),c=t.memoizedState),(i=Rt||Hi(t,n,i,r,g,c,u))?(y||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),o.props=r,o.state=c,o.context=u,r=i):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Du(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:ft(t.type,i),o.props=u,y=t.pendingProps,g=o.context,c=n.contextType,typeof c=="object"&&c!==null?c=ot(c):(c=Qe(n)?pn:Le.current,c=Xn(t,c));var j=n.getDerivedStateFromProps;(x=typeof j=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(i!==y||g!==c)&&Bi(t,o,r,c),Rt=!1,g=t.memoizedState,o.state=g,Wl(t,r,o,l);var w=t.memoizedState;i!==y||g!==w||Xe.current||Rt?(typeof j=="function"&&(Ta(t,n,j,r),w=t.memoizedState),(u=Rt||Hi(t,n,u,r,g,w,c)||!1)?(x||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,w,c),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,w,c)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),o.props=r,o.state=w,o.context=c,r=u):(typeof o.componentDidUpdate!="function"||i===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||i===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),r=!1)}return Ra(e,t,n,r,a,l)}function Ra(e,t,n,r,l,a){od(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return l&&Ti(t,n,!1),At(e,t,a);r=t.stateNode,Vm.current=t;var i=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Kn(t,e.child,null,a),t.child=Kn(t,null,i,a)):Re(e,t,i,a),t.memoizedState=r.state,l&&Ti(t,n,!0),t.child}function id(e){var t=e.stateNode;t.pendingContext?Ai(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ai(e,t.context,!1),So(e,t.containerInfo)}function Yi(e,t,n,r,l){return Qn(),wo(l),t.flags|=256,Re(e,t,n,r),t.child}var Oa={dehydrated:null,treeContext:null,retryLane:0};function $a(e){return{baseLanes:e,cachePool:null,transitions:null}}function cd(e,t,n){var r=t.pendingProps,l=me.current,a=!1,o=(t.flags&128)!==0,i;if((i=o)||(i=e!==null&&e.memoizedState===null?!1:(l&2)!==0),i?(a=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),ce(me,l&1),e===null)return _a(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,a?(r=t.mode,a=t.child,o={mode:"hidden",children:o},!(r&1)&&a!==null?(a.childLanes=0,a.pendingProps=o):a=ps(o,r,0,null),e=fn(e,r,n,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=$a(n),t.memoizedState=Oa,e):To(t,o));if(l=e.memoizedState,l!==null&&(i=l.dehydrated,i!==null))return Wm(e,t,o,r,i,l,n);if(a){a=r.fallback,o=t.mode,l=e.child,i=l.sibling;var c={mode:"hidden",children:r.children};return!(o&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=Yt(l,c),r.subtreeFlags=l.subtreeFlags&14680064),i!==null?a=Yt(i,a):(a=fn(a,o,n,null),a.flags|=2),a.return=t,r.return=t,r.sibling=a,t.child=r,r=a,a=t.child,o=e.child.memoizedState,o=o===null?$a(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},a.memoizedState=o,a.childLanes=e.childLanes&~n,t.memoizedState=Oa,r}return a=e.child,e=a.sibling,r=Yt(a,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function To(e,t){return t=ps({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function ml(e,t,n,r){return r!==null&&wo(r),Kn(t,e.child,null,n),e=To(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Wm(e,t,n,r,l,a,o){if(n)return t.flags&256?(t.flags&=-257,r=Zs(Error(z(422))),ml(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(a=r.fallback,l=t.mode,r=ps({mode:"visible",children:r.children},l,0,null),a=fn(a,l,o,null),a.flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,t.mode&1&&Kn(t,e.child,null,o),t.child.memoizedState=$a(o),t.memoizedState=Oa,a);if(!(t.mode&1))return ml(e,t,o,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var i=r.dgst;return r=i,a=Error(z(419)),r=Zs(a,r,void 0),ml(e,t,o,r)}if(i=(o&e.childLanes)!==0,Ge||i){if(r=Pe,r!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|o)?0:l,l!==0&&l!==a.retryLane&&(a.retryLane=l,_t(e,l),gt(r,e,l,-1))}return $o(),r=Zs(Error(z(421))),ml(e,t,o,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=lp.bind(null,e),l._reactRetry=t,null):(e=a.treeContext,qe=Gt(l.nextSibling),Je=t,fe=!0,pt=null,e!==null&&(rt[lt++]=Et,rt[lt++]=It,rt[lt++]=hn,Et=e.id,It=e.overflow,hn=t),t=To(t,r.children),t.flags|=4096,t)}function Zi(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Aa(e.return,t,n)}function qs(e,t,n,r,l){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=l)}function ud(e,t,n){var r=t.pendingProps,l=r.revealOrder,a=r.tail;if(Re(e,t,r.children,n),r=me.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Zi(e,n,t);else if(e.tag===19)Zi(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ce(me,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&Gl(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),qs(t,!1,l,n,a);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Gl(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}qs(t,!0,n,null,a);break;case"together":qs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Sl(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function At(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),xn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(z(153));if(t.child!==null){for(e=t.child,n=Yt(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Yt(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Gm(e,t,n){switch(t.tag){case 3:id(t),Qn();break;case 5:Lu(t);break;case 1:Qe(t.type)&&$l(t);break;case 4:So(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;ce(Bl,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ce(me,me.current&1),t.flags|=128,null):n&t.child.childLanes?cd(e,t,n):(ce(me,me.current&1),e=At(e,t,n),e!==null?e.sibling:null);ce(me,me.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ud(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),ce(me,me.current),r)break;return null;case 22:case 23:return t.lanes=0,ad(e,t,n)}return At(e,t,n)}var dd,Ua,fd,md;dd=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ua=function(){};fd=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,un(bt.current);var a=null;switch(n){case"input":l=ca(e,l),r=ca(e,r),a=[];break;case"select":l=he({},l,{value:void 0}),r=he({},r,{value:void 0}),a=[];break;case"textarea":l=fa(e,l),r=fa(e,r),a=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Rl)}pa(n,r);var o;n=null;for(u in l)if(!r.hasOwnProperty(u)&&l.hasOwnProperty(u)&&l[u]!=null)if(u==="style"){var i=l[u];for(o in i)i.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Cr.hasOwnProperty(u)?a||(a=[]):(a=a||[]).push(u,null));for(u in r){var c=r[u];if(i=l!=null?l[u]:void 0,r.hasOwnProperty(u)&&c!==i&&(c!=null||i!=null))if(u==="style")if(i){for(o in i)!i.hasOwnProperty(o)||c&&c.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in c)c.hasOwnProperty(o)&&i[o]!==c[o]&&(n||(n={}),n[o]=c[o])}else n||(a||(a=[]),a.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,i=i?i.__html:void 0,c!=null&&i!==c&&(a=a||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(a=a||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Cr.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&ue("scroll",e),a||i===c||(a=[])):(a=a||[]).push(u,c))}n&&(a=a||[]).push("style",n);var u=a;(t.updateQueue=u)&&(t.flags|=4)}};md=function(e,t,n,r){n!==r&&(t.flags|=4)};function cr(e,t){if(!fe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Te(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Xm(e,t,n){var r=t.pendingProps;switch(vo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Te(t),null;case 1:return Qe(t.type)&&Ol(),Te(t),null;case 3:return r=t.stateNode,Yn(),de(Xe),de(Le),Io(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(dl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,pt!==null&&(Ka(pt),pt=null))),Ua(e,t),Te(t),null;case 5:Eo(t);var l=un(Fr.current);if(n=t.type,e!==null&&t.stateNode!=null)fd(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(z(166));return Te(t),null}if(e=un(bt.current),dl(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[Nt]=t,r[Dr]=a,e=(t.mode&1)!==0,n){case"dialog":ue("cancel",r),ue("close",r);break;case"iframe":case"object":case"embed":ue("load",r);break;case"video":case"audio":for(l=0;l<pr.length;l++)ue(pr[l],r);break;case"source":ue("error",r);break;case"img":case"image":case"link":ue("error",r),ue("load",r);break;case"details":ue("toggle",r);break;case"input":ai(r,a),ue("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},ue("invalid",r);break;case"textarea":ii(r,a),ue("invalid",r)}pa(n,a),l=null;for(var o in a)if(a.hasOwnProperty(o)){var i=a[o];o==="children"?typeof i=="string"?r.textContent!==i&&(a.suppressHydrationWarning!==!0&&ul(r.textContent,i,e),l=["children",i]):typeof i=="number"&&r.textContent!==""+i&&(a.suppressHydrationWarning!==!0&&ul(r.textContent,i,e),l=["children",""+i]):Cr.hasOwnProperty(o)&&i!=null&&o==="onScroll"&&ue("scroll",r)}switch(n){case"input":nl(r),oi(r,a,!0);break;case"textarea":nl(r),ci(r);break;case"select":case"option":break;default:typeof a.onClick=="function"&&(r.onclick=Rl)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Uc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[Nt]=t,e[Dr]=r,dd(e,t,!1,!1),t.stateNode=e;e:{switch(o=ha(n,r),n){case"dialog":ue("cancel",e),ue("close",e),l=r;break;case"iframe":case"object":case"embed":ue("load",e),l=r;break;case"video":case"audio":for(l=0;l<pr.length;l++)ue(pr[l],e);l=r;break;case"source":ue("error",e),l=r;break;case"img":case"image":case"link":ue("error",e),ue("load",e),l=r;break;case"details":ue("toggle",e),l=r;break;case"input":ai(e,r),l=ca(e,r),ue("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=he({},r,{value:void 0}),ue("invalid",e);break;case"textarea":ii(e,r),l=fa(e,r),ue("invalid",e);break;default:l=r}pa(n,l),i=l;for(a in i)if(i.hasOwnProperty(a)){var c=i[a];a==="style"?Vc(e,c):a==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&Hc(e,c)):a==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&Sr(e,c):typeof c=="number"&&Sr(e,""+c):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(Cr.hasOwnProperty(a)?c!=null&&a==="onScroll"&&ue("scroll",e):c!=null&&ro(e,a,c,o))}switch(n){case"input":nl(e),oi(e,r,!1);break;case"textarea":nl(e),ci(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Zt(r.value));break;case"select":e.multiple=!!r.multiple,a=r.value,a!=null?On(e,!!r.multiple,a,!1):r.defaultValue!=null&&On(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=Rl)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Te(t),null;case 6:if(e&&t.stateNode!=null)md(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(z(166));if(n=un(Fr.current),un(bt.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[Nt]=t,(a=r.nodeValue!==n)&&(e=Je,e!==null))switch(e.tag){case 3:ul(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&ul(r.nodeValue,n,(e.mode&1)!==0)}a&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Nt]=t,t.stateNode=r}return Te(t),null;case 13:if(de(me),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(fe&&qe!==null&&t.mode&1&&!(t.flags&128))Mu(),Qn(),t.flags|=98560,a=!1;else if(a=dl(t),r!==null&&r.dehydrated!==null){if(e===null){if(!a)throw Error(z(318));if(a=t.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(z(317));a[Nt]=t}else Qn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Te(t),a=!1}else pt!==null&&(Ka(pt),pt=null),a=!0;if(!a)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||me.current&1?Ee===0&&(Ee=3):$o())),t.updateQueue!==null&&(t.flags|=4),Te(t),null);case 4:return Yn(),Ua(e,t),e===null&&Ar(t.stateNode.containerInfo),Te(t),null;case 10:return ko(t.type._context),Te(t),null;case 17:return Qe(t.type)&&Ol(),Te(t),null;case 19:if(de(me),a=t.memoizedState,a===null)return Te(t),null;if(r=(t.flags&128)!==0,o=a.rendering,o===null)if(r)cr(a,!1);else{if(Ee!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=Gl(e),o!==null){for(t.flags|=128,cr(a,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)a=n,e=r,a.flags&=14680066,o=a.alternate,o===null?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=o.childLanes,a.lanes=o.lanes,a.child=o.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=o.memoizedProps,a.memoizedState=o.memoizedState,a.updateQueue=o.updateQueue,a.type=o.type,e=o.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ce(me,me.current&1|2),t.child}e=e.sibling}a.tail!==null&&je()>qn&&(t.flags|=128,r=!0,cr(a,!1),t.lanes=4194304)}else{if(!r)if(e=Gl(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),cr(a,!0),a.tail===null&&a.tailMode==="hidden"&&!o.alternate&&!fe)return Te(t),null}else 2*je()-a.renderingStartTime>qn&&n!==1073741824&&(t.flags|=128,r=!0,cr(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(n=a.last,n!==null?n.sibling=o:t.child=o,a.last=o)}return a.tail!==null?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=je(),t.sibling=null,n=me.current,ce(me,r?n&1|2:n&1),t):(Te(t),null);case 22:case 23:return Oo(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ze&1073741824&&(Te(t),t.subtreeFlags&6&&(t.flags|=8192)):Te(t),null;case 24:return null;case 25:return null}throw Error(z(156,t.tag))}function Qm(e,t){switch(vo(t),t.tag){case 1:return Qe(t.type)&&Ol(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Yn(),de(Xe),de(Le),Io(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Eo(t),null;case 13:if(de(me),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(z(340));Qn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return de(me),null;case 4:return Yn(),null;case 10:return ko(t.type._context),null;case 22:case 23:return Oo(),null;case 24:return null;default:return null}}var pl=!1,De=!1,Km=typeof WeakSet=="function"?WeakSet:Set,V=null;function Fn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ye(e,t,r)}else n.current=null}function Ha(e,t,n){try{n()}catch(r){ye(e,t,r)}}var qi=!1;function Ym(e,t){if(Ca=Dl,e=yu(),xo(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch{n=null;break e}var o=0,i=-1,c=-1,u=0,x=0,y=e,g=null;t:for(;;){for(var j;y!==n||l!==0&&y.nodeType!==3||(i=o+l),y!==a||r!==0&&y.nodeType!==3||(c=o+r),y.nodeType===3&&(o+=y.nodeValue.length),(j=y.firstChild)!==null;)g=y,y=j;for(;;){if(y===e)break t;if(g===n&&++u===l&&(i=o),g===a&&++x===r&&(c=o),(j=y.nextSibling)!==null)break;y=g,g=y.parentNode}y=j}n=i===-1||c===-1?null:{start:i,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Sa={focusedElem:e,selectionRange:n},Dl=!1,V=t;V!==null;)if(t=V,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,V=e;else for(;V!==null;){t=V;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var k=w.memoizedProps,U=w.memoizedState,f=t.stateNode,d=f.getSnapshotBeforeUpdate(t.elementType===t.type?k:ft(t.type,k),U);f.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(z(163))}}catch(p){ye(t,t.return,p)}if(e=t.sibling,e!==null){e.return=t.return,V=e;break}V=t.return}return w=qi,qi=!1,w}function jr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var a=l.destroy;l.destroy=void 0,a!==void 0&&Ha(t,n,a)}l=l.next}while(l!==r)}}function fs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ba(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function pd(e){var t=e.alternate;t!==null&&(e.alternate=null,pd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Nt],delete t[Dr],delete t[Pa],delete t[_m],delete t[Am])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function hd(e){return e.tag===5||e.tag===3||e.tag===4}function Ji(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||hd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Va(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Rl));else if(r!==4&&(e=e.child,e!==null))for(Va(e,t,n),e=e.sibling;e!==null;)Va(e,t,n),e=e.sibling}function Wa(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Wa(e,t,n),e=e.sibling;e!==null;)Wa(e,t,n),e=e.sibling}var ze=null,mt=!1;function Lt(e,t,n){for(n=n.child;n!==null;)gd(e,t,n),n=n.sibling}function gd(e,t,n){if(kt&&typeof kt.onCommitFiberUnmount=="function")try{kt.onCommitFiberUnmount(ls,n)}catch{}switch(n.tag){case 5:De||Fn(n,t);case 6:var r=ze,l=mt;ze=null,Lt(e,t,n),ze=r,mt=l,ze!==null&&(mt?(e=ze,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ze.removeChild(n.stateNode));break;case 18:ze!==null&&(mt?(e=ze,n=n.stateNode,e.nodeType===8?Ws(e.parentNode,n):e.nodeType===1&&Ws(e,n),zr(e)):Ws(ze,n.stateNode));break;case 4:r=ze,l=mt,ze=n.stateNode.containerInfo,mt=!0,Lt(e,t,n),ze=r,mt=l;break;case 0:case 11:case 14:case 15:if(!De&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var a=l,o=a.destroy;a=a.tag,o!==void 0&&(a&2||a&4)&&Ha(n,t,o),l=l.next}while(l!==r)}Lt(e,t,n);break;case 1:if(!De&&(Fn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){ye(n,t,i)}Lt(e,t,n);break;case 21:Lt(e,t,n);break;case 22:n.mode&1?(De=(r=De)||n.memoizedState!==null,Lt(e,t,n),De=r):Lt(e,t,n);break;default:Lt(e,t,n)}}function ec(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Km),t.forEach(function(r){var l=sp.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function dt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var a=e,o=t,i=o;e:for(;i!==null;){switch(i.tag){case 5:ze=i.stateNode,mt=!1;break e;case 3:ze=i.stateNode.containerInfo,mt=!0;break e;case 4:ze=i.stateNode.containerInfo,mt=!0;break e}i=i.return}if(ze===null)throw Error(z(160));gd(a,o,l),ze=null,mt=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(u){ye(l,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)xd(t,e),t=t.sibling}function xd(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(dt(t,e),wt(e),r&4){try{jr(3,e,e.return),fs(3,e)}catch(k){ye(e,e.return,k)}try{jr(5,e,e.return)}catch(k){ye(e,e.return,k)}}break;case 1:dt(t,e),wt(e),r&512&&n!==null&&Fn(n,n.return);break;case 5:if(dt(t,e),wt(e),r&512&&n!==null&&Fn(n,n.return),e.flags&32){var l=e.stateNode;try{Sr(l,"")}catch(k){ye(e,e.return,k)}}if(r&4&&(l=e.stateNode,l!=null)){var a=e.memoizedProps,o=n!==null?n.memoizedProps:a,i=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{i==="input"&&a.type==="radio"&&a.name!=null&&Oc(l,a),ha(i,o);var u=ha(i,a);for(o=0;o<c.length;o+=2){var x=c[o],y=c[o+1];x==="style"?Vc(l,y):x==="dangerouslySetInnerHTML"?Hc(l,y):x==="children"?Sr(l,y):ro(l,x,y,u)}switch(i){case"input":ua(l,a);break;case"textarea":$c(l,a);break;case"select":var g=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!a.multiple;var j=a.value;j!=null?On(l,!!a.multiple,j,!1):g!==!!a.multiple&&(a.defaultValue!=null?On(l,!!a.multiple,a.defaultValue,!0):On(l,!!a.multiple,a.multiple?[]:"",!1))}l[Dr]=a}catch(k){ye(e,e.return,k)}}break;case 6:if(dt(t,e),wt(e),r&4){if(e.stateNode===null)throw Error(z(162));l=e.stateNode,a=e.memoizedProps;try{l.nodeValue=a}catch(k){ye(e,e.return,k)}}break;case 3:if(dt(t,e),wt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{zr(t.containerInfo)}catch(k){ye(e,e.return,k)}break;case 4:dt(t,e),wt(e);break;case 13:dt(t,e),wt(e),l=e.child,l.flags&8192&&(a=l.memoizedState!==null,l.stateNode.isHidden=a,!a||l.alternate!==null&&l.alternate.memoizedState!==null||(Fo=je())),r&4&&ec(e);break;case 22:if(x=n!==null&&n.memoizedState!==null,e.mode&1?(De=(u=De)||x,dt(t,e),De=u):dt(t,e),wt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!x&&e.mode&1)for(V=e,x=e.child;x!==null;){for(y=V=x;V!==null;){switch(g=V,j=g.child,g.tag){case 0:case 11:case 14:case 15:jr(4,g,g.return);break;case 1:Fn(g,g.return);var w=g.stateNode;if(typeof w.componentWillUnmount=="function"){r=g,n=g.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(k){ye(r,n,k)}}break;case 5:Fn(g,g.return);break;case 22:if(g.memoizedState!==null){nc(y);continue}}j!==null?(j.return=g,V=j):nc(y)}x=x.sibling}e:for(x=null,y=e;;){if(y.tag===5){if(x===null){x=y;try{l=y.stateNode,u?(a=l.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none"):(i=y.stateNode,c=y.memoizedProps.style,o=c!=null&&c.hasOwnProperty("display")?c.display:null,i.style.display=Bc("display",o))}catch(k){ye(e,e.return,k)}}}else if(y.tag===6){if(x===null)try{y.stateNode.nodeValue=u?"":y.memoizedProps}catch(k){ye(e,e.return,k)}}else if((y.tag!==22&&y.tag!==23||y.memoizedState===null||y===e)&&y.child!==null){y.child.return=y,y=y.child;continue}if(y===e)break e;for(;y.sibling===null;){if(y.return===null||y.return===e)break e;x===y&&(x=null),y=y.return}x===y&&(x=null),y.sibling.return=y.return,y=y.sibling}}break;case 19:dt(t,e),wt(e),r&4&&ec(e);break;case 21:break;default:dt(t,e),wt(e)}}function wt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(hd(n)){var r=n;break e}n=n.return}throw Error(z(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Sr(l,""),r.flags&=-33);var a=Ji(e);Wa(e,a,l);break;case 3:case 4:var o=r.stateNode.containerInfo,i=Ji(e);Va(e,i,o);break;default:throw Error(z(161))}}catch(c){ye(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Zm(e,t,n){V=e,yd(e)}function yd(e,t,n){for(var r=(e.mode&1)!==0;V!==null;){var l=V,a=l.child;if(l.tag===22&&r){var o=l.memoizedState!==null||pl;if(!o){var i=l.alternate,c=i!==null&&i.memoizedState!==null||De;i=pl;var u=De;if(pl=o,(De=c)&&!u)for(V=l;V!==null;)o=V,c=o.child,o.tag===22&&o.memoizedState!==null?rc(l):c!==null?(c.return=o,V=c):rc(l);for(;a!==null;)V=a,yd(a),a=a.sibling;V=l,pl=i,De=u}tc(e)}else l.subtreeFlags&8772&&a!==null?(a.return=l,V=a):tc(e)}}function tc(e){for(;V!==null;){var t=V;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:De||fs(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!De)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:ft(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;a!==null&&Oi(t,a,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Oi(t,o,n)}break;case 5:var i=t.stateNode;if(n===null&&t.flags&4){n=i;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var x=u.memoizedState;if(x!==null){var y=x.dehydrated;y!==null&&zr(y)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(z(163))}De||t.flags&512&&Ba(t)}catch(g){ye(t,t.return,g)}}if(t===e){V=null;break}if(n=t.sibling,n!==null){n.return=t.return,V=n;break}V=t.return}}function nc(e){for(;V!==null;){var t=V;if(t===e){V=null;break}var n=t.sibling;if(n!==null){n.return=t.return,V=n;break}V=t.return}}function rc(e){for(;V!==null;){var t=V;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{fs(4,t)}catch(c){ye(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(c){ye(t,l,c)}}var a=t.return;try{Ba(t)}catch(c){ye(t,a,c)}break;case 5:var o=t.return;try{Ba(t)}catch(c){ye(t,o,c)}}}catch(c){ye(t,t.return,c)}if(t===e){V=null;break}var i=t.sibling;if(i!==null){i.return=t.return,V=i;break}V=t.return}}var qm=Math.ceil,Kl=Tt.ReactCurrentDispatcher,Do=Tt.ReactCurrentOwner,at=Tt.ReactCurrentBatchConfig,le=0,Pe=null,Ne=null,Me=0,Ze=0,Rn=en(0),Ee=0,Ur=null,xn=0,ms=0,Lo=0,Nr=null,We=null,Fo=0,qn=1/0,Ct=null,Yl=!1,Ga=null,Qt=null,hl=!1,Ht=null,Zl=0,kr=0,Xa=null,El=-1,Il=0;function Oe(){return le&6?je():El!==-1?El:El=je()}function Kt(e){return e.mode&1?le&2&&Me!==0?Me&-Me:Dm.transition!==null?(Il===0&&(Il=nu()),Il):(e=se,e!==0||(e=window.event,e=e===void 0?16:cu(e.type)),e):1}function gt(e,t,n,r){if(50<kr)throw kr=0,Xa=null,Error(z(185));Br(e,n,r),(!(le&2)||e!==Pe)&&(e===Pe&&(!(le&2)&&(ms|=n),Ee===4&&$t(e,Me)),Ke(e,r),n===1&&le===0&&!(t.mode&1)&&(qn=je()+500,cs&&tn()))}function Ke(e,t){var n=e.callbackNode;Df(e,t);var r=Tl(e,e===Pe?Me:0);if(r===0)n!==null&&fi(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&fi(n),t===1)e.tag===0?Tm(lc.bind(null,e)):Iu(lc.bind(null,e)),zm(function(){!(le&6)&&tn()}),n=null;else{switch(ru(r)){case 1:n=io;break;case 4:n=eu;break;case 16:n=Al;break;case 536870912:n=tu;break;default:n=Al}n=Sd(n,vd.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function vd(e,t){if(El=-1,Il=0,le&6)throw Error(z(327));var n=e.callbackNode;if(Vn()&&e.callbackNode!==n)return null;var r=Tl(e,e===Pe?Me:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=ql(e,r);else{t=r;var l=le;le|=2;var a=jd();(Pe!==e||Me!==t)&&(Ct=null,qn=je()+500,dn(e,t));do try{tp();break}catch(i){wd(e,i)}while(!0);No(),Kl.current=a,le=l,Ne!==null?t=0:(Pe=null,Me=0,t=Ee)}if(t!==0){if(t===2&&(l=wa(e),l!==0&&(r=l,t=Qa(e,l))),t===1)throw n=Ur,dn(e,0),$t(e,r),Ke(e,je()),n;if(t===6)$t(e,r);else{if(l=e.current.alternate,!(r&30)&&!Jm(l)&&(t=ql(e,r),t===2&&(a=wa(e),a!==0&&(r=a,t=Qa(e,a))),t===1))throw n=Ur,dn(e,0),$t(e,r),Ke(e,je()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(z(345));case 2:an(e,We,Ct);break;case 3:if($t(e,r),(r&130023424)===r&&(t=Fo+500-je(),10<t)){if(Tl(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){Oe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Ia(an.bind(null,e,We,Ct),t);break}an(e,We,Ct);break;case 4:if($t(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var o=31-ht(r);a=1<<o,o=t[o],o>l&&(l=o),r&=~a}if(r=l,r=je()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*qm(r/1960))-r,10<r){e.timeoutHandle=Ia(an.bind(null,e,We,Ct),r);break}an(e,We,Ct);break;case 5:an(e,We,Ct);break;default:throw Error(z(329))}}}return Ke(e,je()),e.callbackNode===n?vd.bind(null,e):null}function Qa(e,t){var n=Nr;return e.current.memoizedState.isDehydrated&&(dn(e,t).flags|=256),e=ql(e,t),e!==2&&(t=We,We=n,t!==null&&Ka(t)),e}function Ka(e){We===null?We=e:We.push.apply(We,e)}function Jm(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],a=l.getSnapshot;l=l.value;try{if(!xt(a(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function $t(e,t){for(t&=~Lo,t&=~ms,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ht(t),r=1<<n;e[n]=-1,t&=~r}}function lc(e){if(le&6)throw Error(z(327));Vn();var t=Tl(e,0);if(!(t&1))return Ke(e,je()),null;var n=ql(e,t);if(e.tag!==0&&n===2){var r=wa(e);r!==0&&(t=r,n=Qa(e,r))}if(n===1)throw n=Ur,dn(e,0),$t(e,t),Ke(e,je()),n;if(n===6)throw Error(z(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,an(e,We,Ct),Ke(e,je()),null}function Ro(e,t){var n=le;le|=1;try{return e(t)}finally{le=n,le===0&&(qn=je()+500,cs&&tn())}}function yn(e){Ht!==null&&Ht.tag===0&&!(le&6)&&Vn();var t=le;le|=1;var n=at.transition,r=se;try{if(at.transition=null,se=1,e)return e()}finally{se=r,at.transition=n,le=t,!(le&6)&&tn()}}function Oo(){Ze=Rn.current,de(Rn)}function dn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Pm(n)),Ne!==null)for(n=Ne.return;n!==null;){var r=n;switch(vo(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ol();break;case 3:Yn(),de(Xe),de(Le),Io();break;case 5:Eo(r);break;case 4:Yn();break;case 13:de(me);break;case 19:de(me);break;case 10:ko(r.type._context);break;case 22:case 23:Oo()}n=n.return}if(Pe=e,Ne=e=Yt(e.current,null),Me=Ze=t,Ee=0,Ur=null,Lo=ms=xn=0,We=Nr=null,cn!==null){for(t=0;t<cn.length;t++)if(n=cn[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,a=n.pending;if(a!==null){var o=a.next;a.next=l,r.next=o}n.pending=r}cn=null}return e}function wd(e,t){do{var n=Ne;try{if(No(),bl.current=Ql,Xl){for(var r=pe.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}Xl=!1}if(gn=0,Ie=Se=pe=null,wr=!1,Rr=0,Do.current=null,n===null||n.return===null){Ee=1,Ur=t,Ne=null;break}e:{var a=e,o=n.return,i=n,c=t;if(t=Me,i.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,x=i,y=x.tag;if(!(x.mode&1)&&(y===0||y===11||y===15)){var g=x.alternate;g?(x.updateQueue=g.updateQueue,x.memoizedState=g.memoizedState,x.lanes=g.lanes):(x.updateQueue=null,x.memoizedState=null)}var j=Wi(o);if(j!==null){j.flags&=-257,Gi(j,o,i,a,t),j.mode&1&&Vi(a,u,t),t=j,c=u;var w=t.updateQueue;if(w===null){var k=new Set;k.add(c),t.updateQueue=k}else w.add(c);break e}else{if(!(t&1)){Vi(a,u,t),$o();break e}c=Error(z(426))}}else if(fe&&i.mode&1){var U=Wi(o);if(U!==null){!(U.flags&65536)&&(U.flags|=256),Gi(U,o,i,a,t),wo(Zn(c,i));break e}}a=c=Zn(c,i),Ee!==4&&(Ee=2),Nr===null?Nr=[a]:Nr.push(a),a=o;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t;var f=rd(a,c,t);Ri(a,f);break e;case 1:i=c;var d=a.type,m=a.stateNode;if(!(a.flags&128)&&(typeof d.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(Qt===null||!Qt.has(m)))){a.flags|=65536,t&=-t,a.lanes|=t;var p=ld(a,i,t);Ri(a,p);break e}}a=a.return}while(a!==null)}kd(n)}catch(b){t=b,Ne===n&&n!==null&&(Ne=n=n.return);continue}break}while(!0)}function jd(){var e=Kl.current;return Kl.current=Ql,e===null?Ql:e}function $o(){(Ee===0||Ee===3||Ee===2)&&(Ee=4),Pe===null||!(xn&268435455)&&!(ms&268435455)||$t(Pe,Me)}function ql(e,t){var n=le;le|=2;var r=jd();(Pe!==e||Me!==t)&&(Ct=null,dn(e,t));do try{ep();break}catch(l){wd(e,l)}while(!0);if(No(),le=n,Kl.current=r,Ne!==null)throw Error(z(261));return Pe=null,Me=0,Ee}function ep(){for(;Ne!==null;)Nd(Ne)}function tp(){for(;Ne!==null&&!Sf();)Nd(Ne)}function Nd(e){var t=Cd(e.alternate,e,Ze);e.memoizedProps=e.pendingProps,t===null?kd(e):Ne=t,Do.current=null}function kd(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Qm(n,t),n!==null){n.flags&=32767,Ne=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ee=6,Ne=null;return}}else if(n=Xm(n,t,Ze),n!==null){Ne=n;return}if(t=t.sibling,t!==null){Ne=t;return}Ne=t=e}while(t!==null);Ee===0&&(Ee=5)}function an(e,t,n){var r=se,l=at.transition;try{at.transition=null,se=1,np(e,t,n,r)}finally{at.transition=l,se=r}return null}function np(e,t,n,r){do Vn();while(Ht!==null);if(le&6)throw Error(z(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(z(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(Lf(e,a),e===Pe&&(Ne=Pe=null,Me=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||hl||(hl=!0,Sd(Al,function(){return Vn(),null})),a=(n.flags&15990)!==0,n.subtreeFlags&15990||a){a=at.transition,at.transition=null;var o=se;se=1;var i=le;le|=4,Do.current=null,Ym(e,n),xd(n,e),Nm(Sa),Dl=!!Ca,Sa=Ca=null,e.current=n,Zm(n),Ef(),le=i,se=o,at.transition=a}else e.current=n;if(hl&&(hl=!1,Ht=e,Zl=l),a=e.pendingLanes,a===0&&(Qt=null),zf(n.stateNode),Ke(e,je()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(Yl)throw Yl=!1,e=Ga,Ga=null,e;return Zl&1&&e.tag!==0&&Vn(),a=e.pendingLanes,a&1?e===Xa?kr++:(kr=0,Xa=e):kr=0,tn(),null}function Vn(){if(Ht!==null){var e=ru(Zl),t=at.transition,n=se;try{if(at.transition=null,se=16>e?16:e,Ht===null)var r=!1;else{if(e=Ht,Ht=null,Zl=0,le&6)throw Error(z(331));var l=le;for(le|=4,V=e.current;V!==null;){var a=V,o=a.child;if(V.flags&16){var i=a.deletions;if(i!==null){for(var c=0;c<i.length;c++){var u=i[c];for(V=u;V!==null;){var x=V;switch(x.tag){case 0:case 11:case 15:jr(8,x,a)}var y=x.child;if(y!==null)y.return=x,V=y;else for(;V!==null;){x=V;var g=x.sibling,j=x.return;if(pd(x),x===u){V=null;break}if(g!==null){g.return=j,V=g;break}V=j}}}var w=a.alternate;if(w!==null){var k=w.child;if(k!==null){w.child=null;do{var U=k.sibling;k.sibling=null,k=U}while(k!==null)}}V=a}}if(a.subtreeFlags&2064&&o!==null)o.return=a,V=o;else e:for(;V!==null;){if(a=V,a.flags&2048)switch(a.tag){case 0:case 11:case 15:jr(9,a,a.return)}var f=a.sibling;if(f!==null){f.return=a.return,V=f;break e}V=a.return}}var d=e.current;for(V=d;V!==null;){o=V;var m=o.child;if(o.subtreeFlags&2064&&m!==null)m.return=o,V=m;else e:for(o=d;V!==null;){if(i=V,i.flags&2048)try{switch(i.tag){case 0:case 11:case 15:fs(9,i)}}catch(b){ye(i,i.return,b)}if(i===o){V=null;break e}var p=i.sibling;if(p!==null){p.return=i.return,V=p;break e}V=i.return}}if(le=l,tn(),kt&&typeof kt.onPostCommitFiberRoot=="function")try{kt.onPostCommitFiberRoot(ls,e)}catch{}r=!0}return r}finally{se=n,at.transition=t}}return!1}function sc(e,t,n){t=Zn(n,t),t=rd(e,t,1),e=Xt(e,t,1),t=Oe(),e!==null&&(Br(e,1,t),Ke(e,t))}function ye(e,t,n){if(e.tag===3)sc(e,e,n);else for(;t!==null;){if(t.tag===3){sc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Qt===null||!Qt.has(r))){e=Zn(n,e),e=ld(t,e,1),t=Xt(t,e,1),e=Oe(),t!==null&&(Br(t,1,e),Ke(t,e));break}}t=t.return}}function rp(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Oe(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Me&n)===n&&(Ee===4||Ee===3&&(Me&130023424)===Me&&500>je()-Fo?dn(e,0):Lo|=n),Ke(e,t)}function bd(e,t){t===0&&(e.mode&1?(t=sl,sl<<=1,!(sl&130023424)&&(sl=4194304)):t=1);var n=Oe();e=_t(e,t),e!==null&&(Br(e,t,n),Ke(e,n))}function lp(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),bd(e,n)}function sp(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(z(314))}r!==null&&r.delete(t),bd(e,n)}var Cd;Cd=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Xe.current)Ge=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ge=!1,Gm(e,t,n);Ge=!!(e.flags&131072)}else Ge=!1,fe&&t.flags&1048576&&Pu(t,Hl,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Sl(e,t),e=t.pendingProps;var l=Xn(t,Le.current);Bn(t,n),l=zo(null,t,r,e,l,n);var a=Mo();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Qe(r)?(a=!0,$l(t)):a=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Co(t),l.updater=ds,t.stateNode=l,l._reactInternals=t,Da(t,r,e,n),t=Ra(null,t,r,!0,a,n)):(t.tag=0,fe&&a&&yo(t),Re(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Sl(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=op(r),e=ft(r,e),l){case 0:t=Fa(null,t,r,e,n);break e;case 1:t=Ki(null,t,r,e,n);break e;case 11:t=Xi(null,t,r,e,n);break e;case 14:t=Qi(null,t,r,ft(r.type,e),n);break e}throw Error(z(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ft(r,l),Fa(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ft(r,l),Ki(e,t,r,l,n);case 3:e:{if(id(t),e===null)throw Error(z(387));r=t.pendingProps,a=t.memoizedState,l=a.element,Du(e,t),Wl(t,r,null,n);var o=t.memoizedState;if(r=o.element,a.isDehydrated)if(a={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=a,t.memoizedState=a,t.flags&256){l=Zn(Error(z(423)),t),t=Yi(e,t,r,n,l);break e}else if(r!==l){l=Zn(Error(z(424)),t),t=Yi(e,t,r,n,l);break e}else for(qe=Gt(t.stateNode.containerInfo.firstChild),Je=t,fe=!0,pt=null,n=Au(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Qn(),r===l){t=At(e,t,n);break e}Re(e,t,r,n)}t=t.child}return t;case 5:return Lu(t),e===null&&_a(t),r=t.type,l=t.pendingProps,a=e!==null?e.memoizedProps:null,o=l.children,Ea(r,l)?o=null:a!==null&&Ea(r,a)&&(t.flags|=32),od(e,t),Re(e,t,o,n),t.child;case 6:return e===null&&_a(t),null;case 13:return cd(e,t,n);case 4:return So(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Kn(t,null,r,n):Re(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ft(r,l),Xi(e,t,r,l,n);case 7:return Re(e,t,t.pendingProps,n),t.child;case 8:return Re(e,t,t.pendingProps.children,n),t.child;case 12:return Re(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,a=t.memoizedProps,o=l.value,ce(Bl,r._currentValue),r._currentValue=o,a!==null)if(xt(a.value,o)){if(a.children===l.children&&!Xe.current){t=At(e,t,n);break e}}else for(a=t.child,a!==null&&(a.return=t);a!==null;){var i=a.dependencies;if(i!==null){o=a.child;for(var c=i.firstContext;c!==null;){if(c.context===r){if(a.tag===1){c=Pt(-1,n&-n),c.tag=2;var u=a.updateQueue;if(u!==null){u=u.shared;var x=u.pending;x===null?c.next=c:(c.next=x.next,x.next=c),u.pending=c}}a.lanes|=n,c=a.alternate,c!==null&&(c.lanes|=n),Aa(a.return,n,t),i.lanes|=n;break}c=c.next}}else if(a.tag===10)o=a.type===t.type?null:a.child;else if(a.tag===18){if(o=a.return,o===null)throw Error(z(341));o.lanes|=n,i=o.alternate,i!==null&&(i.lanes|=n),Aa(o,n,t),o=a.sibling}else o=a.child;if(o!==null)o.return=a;else for(o=a;o!==null;){if(o===t){o=null;break}if(a=o.sibling,a!==null){a.return=o.return,o=a;break}o=o.return}a=o}Re(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,Bn(t,n),l=ot(l),r=r(l),t.flags|=1,Re(e,t,r,n),t.child;case 14:return r=t.type,l=ft(r,t.pendingProps),l=ft(r.type,l),Qi(e,t,r,l,n);case 15:return sd(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ft(r,l),Sl(e,t),t.tag=1,Qe(r)?(e=!0,$l(t)):e=!1,Bn(t,n),nd(t,r,l),Da(t,r,l,n),Ra(null,t,r,!0,e,n);case 19:return ud(e,t,n);case 22:return ad(e,t,n)}throw Error(z(156,t.tag))};function Sd(e,t){return Jc(e,t)}function ap(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function st(e,t,n,r){return new ap(e,t,n,r)}function Uo(e){return e=e.prototype,!(!e||!e.isReactComponent)}function op(e){if(typeof e=="function")return Uo(e)?1:0;if(e!=null){if(e=e.$$typeof,e===so)return 11;if(e===ao)return 14}return 2}function Yt(e,t){var n=e.alternate;return n===null?(n=st(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Pl(e,t,n,r,l,a){var o=2;if(r=e,typeof e=="function")Uo(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case In:return fn(n.children,l,a,t);case lo:o=8,l|=8;break;case sa:return e=st(12,n,t,l|2),e.elementType=sa,e.lanes=a,e;case aa:return e=st(13,n,t,l),e.elementType=aa,e.lanes=a,e;case oa:return e=st(19,n,t,l),e.elementType=oa,e.lanes=a,e;case Lc:return ps(n,l,a,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Tc:o=10;break e;case Dc:o=9;break e;case so:o=11;break e;case ao:o=14;break e;case Ft:o=16,r=null;break e}throw Error(z(130,e==null?e:typeof e,""))}return t=st(o,n,t,l),t.elementType=e,t.type=r,t.lanes=a,t}function fn(e,t,n,r){return e=st(7,e,r,t),e.lanes=n,e}function ps(e,t,n,r){return e=st(22,e,r,t),e.elementType=Lc,e.lanes=n,e.stateNode={isHidden:!1},e}function Js(e,t,n){return e=st(6,e,null,t),e.lanes=n,e}function ea(e,t,n){return t=st(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ip(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ts(0),this.expirationTimes=Ts(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ts(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function Ho(e,t,n,r,l,a,o,i,c){return e=new ip(e,t,n,i,c),t===1?(t=1,a===!0&&(t|=8)):t=0,a=st(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Co(a),e}function cp(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:En,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Ed(e){if(!e)return qt;e=e._reactInternals;e:{if(jn(e)!==e||e.tag!==1)throw Error(z(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Qe(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(z(171))}if(e.tag===1){var n=e.type;if(Qe(n))return Eu(e,n,t)}return t}function Id(e,t,n,r,l,a,o,i,c){return e=Ho(n,r,!0,e,l,a,o,i,c),e.context=Ed(null),n=e.current,r=Oe(),l=Kt(n),a=Pt(r,l),a.callback=t??null,Xt(n,a,l),e.current.lanes=l,Br(e,l,r),Ke(e,r),e}function hs(e,t,n,r){var l=t.current,a=Oe(),o=Kt(l);return n=Ed(n),t.context===null?t.context=n:t.pendingContext=n,t=Pt(a,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Xt(l,t,o),e!==null&&(gt(e,l,o,a),kl(e,l,o)),o}function Jl(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ac(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Bo(e,t){ac(e,t),(e=e.alternate)&&ac(e,t)}function up(){return null}var Pd=typeof reportError=="function"?reportError:function(e){console.error(e)};function Vo(e){this._internalRoot=e}gs.prototype.render=Vo.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(z(409));hs(e,t,null,null)};gs.prototype.unmount=Vo.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;yn(function(){hs(null,e,null,null)}),t[Mt]=null}};function gs(e){this._internalRoot=e}gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=au();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ot.length&&t!==0&&t<Ot[n].priority;n++);Ot.splice(n,0,e),n===0&&iu(e)}};function Wo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function xs(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function oc(){}function dp(e,t,n,r,l){if(l){if(typeof r=="function"){var a=r;r=function(){var u=Jl(o);a.call(u)}}var o=Id(t,r,e,0,null,!1,!1,"",oc);return e._reactRootContainer=o,e[Mt]=o.current,Ar(e.nodeType===8?e.parentNode:e),yn(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var i=r;r=function(){var u=Jl(c);i.call(u)}}var c=Ho(e,0,!1,null,null,!1,!1,"",oc);return e._reactRootContainer=c,e[Mt]=c.current,Ar(e.nodeType===8?e.parentNode:e),yn(function(){hs(t,c,n,r)}),c}function ys(e,t,n,r,l){var a=n._reactRootContainer;if(a){var o=a;if(typeof l=="function"){var i=l;l=function(){var c=Jl(o);i.call(c)}}hs(t,o,e,l)}else o=dp(n,t,e,l,r);return Jl(o)}lu=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=mr(t.pendingLanes);n!==0&&(co(t,n|1),Ke(t,je()),!(le&6)&&(qn=je()+500,tn()))}break;case 13:yn(function(){var r=_t(e,1);if(r!==null){var l=Oe();gt(r,e,1,l)}}),Bo(e,1)}};uo=function(e){if(e.tag===13){var t=_t(e,134217728);if(t!==null){var n=Oe();gt(t,e,134217728,n)}Bo(e,134217728)}};su=function(e){if(e.tag===13){var t=Kt(e),n=_t(e,t);if(n!==null){var r=Oe();gt(n,e,t,r)}Bo(e,t)}};au=function(){return se};ou=function(e,t){var n=se;try{return se=e,t()}finally{se=n}};xa=function(e,t,n){switch(t){case"input":if(ua(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=is(r);if(!l)throw Error(z(90));Rc(r),ua(r,l)}}}break;case"textarea":$c(e,n);break;case"select":t=n.value,t!=null&&On(e,!!n.multiple,t,!1)}};Xc=Ro;Qc=yn;var fp={usingClientEntryPoint:!1,Events:[Wr,_n,is,Wc,Gc,Ro]},ur={findFiberByHostInstance:on,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},mp={bundleType:ur.bundleType,version:ur.version,rendererPackageName:ur.rendererPackageName,rendererConfig:ur.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Tt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Zc(e),e===null?null:e.stateNode},findFiberByHostInstance:ur.findFiberByHostInstance||up,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var gl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!gl.isDisabled&&gl.supportsFiber)try{ls=gl.inject(mp),kt=gl}catch{}}tt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fp;tt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Wo(t))throw Error(z(200));return cp(e,t,null,n)};tt.createRoot=function(e,t){if(!Wo(e))throw Error(z(299));var n=!1,r="",l=Pd;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=Ho(e,1,!1,null,null,n,!1,r,l),e[Mt]=t.current,Ar(e.nodeType===8?e.parentNode:e),new Vo(t)};tt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(z(188)):(e=Object.keys(e).join(","),Error(z(268,e)));return e=Zc(t),e=e===null?null:e.stateNode,e};tt.flushSync=function(e){return yn(e)};tt.hydrate=function(e,t,n){if(!xs(t))throw Error(z(200));return ys(null,e,t,!0,n)};tt.hydrateRoot=function(e,t,n){if(!Wo(e))throw Error(z(405));var r=n!=null&&n.hydratedSources||null,l=!1,a="",o=Pd;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(a=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=Id(t,null,e,1,n??null,l,!1,a,o),e[Mt]=t.current,Ar(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new gs(t)};tt.render=function(e,t,n){if(!xs(t))throw Error(z(200));return ys(null,e,t,!1,n)};tt.unmountComponentAtNode=function(e){if(!xs(e))throw Error(z(40));return e._reactRootContainer?(yn(function(){ys(null,null,e,!1,function(){e._reactRootContainer=null,e[Mt]=null})}),!0):!1};tt.unstable_batchedUpdates=Ro;tt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!xs(n))throw Error(z(200));if(e==null||e._reactInternals===void 0)throw Error(z(38));return ys(e,t,n,!1,r)};tt.version="18.3.1-next-f1338f8080-20240426";function zd(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(zd)}catch(e){console.error(e)}}zd(),zc.exports=tt;var pp=zc.exports,ic=pp;ra.createRoot=ic.createRoot,ra.hydrateRoot=ic.hydrateRoot;/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var hp={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gp=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),X=(e,t)=>{const n=N.forwardRef(({color:r="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:o,className:i="",children:c,...u},x)=>N.createElement("svg",{ref:x,...hp,width:l,height:l,stroke:r,strokeWidth:o?Number(a)*24/Number(l):a,className:["lucide",`lucide-${gp(e)}`,i].join(" "),...u},[...t.map(([y,g])=>N.createElement(y,g)),...Array.isArray(c)?c:[c]]));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Go=X("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xp=X("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yp=X("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vp=X("ArrowUpNarrowWide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cc=X("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ta=X("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uc=X("Brush",[["path",{d:"m9.06 11.9 8.07-8.06a2.85 2.85 0 1 1 4.03 4.03l-8.06 8.08",key:"1styjt"}],["path",{d:"M7.07 14.94c-1.66 0-3 1.35-3 3.02 0 1.33-2.5 1.52-2 2.02 1.08 1.1 2.49 2.02 4 2.02 2.2 0 4-1.8 4-4.04a3.01 3.01 0 0 0-3-3.02z",key:"z0l1mu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wp=X("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jp=X("CheckSquare",[["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}],["path",{d:"M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11",key:"1jnkn4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Np=X("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kp=X("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bp=X("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=X("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cp=X("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sp=X("Crop",[["path",{d:"M6 2v14a2 2 0 0 0 2 2h14",key:"ron5a4"}],["path",{d:"M18 22V8a2 2 0 0 0-2-2H2",key:"7s9ehn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xr=X("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ep=X("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ip=X("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Md=X("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pp=X("FolderDown",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}],["path",{d:"M12 10v6",key:"1bos4e"}],["path",{d:"m15 13-3 3-3-3",key:"6j2sf0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xo=X("FolderPlus",[["path",{d:"M12 10v6",key:"1bos4e"}],["path",{d:"M9 13h6",key:"1uhe8q"}],["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const br=X("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zp=X("GitCompare",[["circle",{cx:"18",cy:"18",r:"3",key:"1xkwt0"}],["circle",{cx:"6",cy:"6",r:"3",key:"1lh9wr"}],["path",{d:"M13 6h3a2 2 0 0 1 2 2v7",key:"1yeb86"}],["path",{d:"M11 18H8a2 2 0 0 1-2-2V9",key:"19pyzm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mp=X("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wn=X("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _p=X("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ap=X("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tp=X("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dc=X("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dp=X("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _d=X("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lp=X("Paperclip",[["path",{d:"m21.44 11.05-9.19 9.19a6 6 0 0 1-8.49-8.49l8.57-8.57A4 4 0 1 1 18 8.84l-8.59 8.57a2 2 0 0 1-2.83-2.83l8.49-8.48",key:"1u3ebp"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qr=X("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.5 3.5a2.12 2.12 0 0 1 3 3L7 19l-4 1 1-4Z",key:"ymcmye"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mn=X("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fp=X("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ya=X("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rp=X("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Op=X("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=X("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fc=X("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $p=X("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=X("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Up=X("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mc=X("Shuffle",[["path",{d:"M2 18h1.4c1.3 0 2.5-.6 3.3-1.7l6.1-8.6c.7-1.1 2-1.7 3.3-1.7H22",key:"1wmou1"}],["path",{d:"m18 2 4 4-4 4",key:"pucp1d"}],["path",{d:"M2 6h1.9c1.5 0 2.9.9 3.6 2.2",key:"10bdb2"}],["path",{d:"M22 18h-5.9c-1.3 0-2.6-.7-3.3-1.8l-.5-.8",key:"vgxac0"}],["path",{d:"m18 14 4 4-4 4",key:"10pe0f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hp=X("Sliders",[["line",{x1:"4",x2:"4",y1:"21",y2:"14",key:"1p332r"}],["line",{x1:"4",x2:"4",y1:"10",y2:"3",key:"gb41h5"}],["line",{x1:"12",x2:"12",y1:"21",y2:"12",key:"hf2csr"}],["line",{x1:"12",x2:"12",y1:"8",y2:"3",key:"1kfi7u"}],["line",{x1:"20",x2:"20",y1:"21",y2:"16",key:"1lhrwl"}],["line",{x1:"20",x2:"20",y1:"12",y2:"3",key:"16vvfq"}],["line",{x1:"2",x2:"6",y1:"14",y2:"14",key:"1uebub"}],["line",{x1:"10",x2:"14",y1:"8",y2:"8",key:"1yglbp"}],["line",{x1:"18",x2:"22",y1:"16",y2:"16",key:"1jxqpz"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vn=X("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Td=X("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nn=X("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pc=X("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bp=X("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vp=X("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hc=X("Wand2",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72Z",key:"1bcowg"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wp=X("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=X("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gp=X("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xp=X("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qp=X("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),gc=async function(e){e.headers.set("Origin","https://labs.google"),e.headers.set("Referer","https://labs.google");try{const t=await fetch(e.url,{body:e.body,method:e.method,headers:e.headers}),n=await t.text();return t.ok?{Ok:n}:{Err:new Error(n)}}catch(t){return{Err:t instanceof Error?t:new Error("Failed to fetch.")}}};var ns,Dd;class xc{constructor(t){qo(this,ns);ln(this,"credentials");if(!t.authorizationKey&&!t.cookie)throw new Error("Authorization token and Cookie both are missing.");if(t.cookie&&t.cookie.length<70)throw new Error("Invalid cookie was provided.");this.credentials=structuredClone(t),this.credentials.cookie&&!this.credentials.cookie.startsWith("__Secure-next-auth.session-token=")&&(this.credentials.cookie="__Secure-next-auth.session-token="+this.credentials.cookie)}async getAuthToken(t=!1){if(!this.credentials.cookie)return{Err:new Error("Cookie is required for generating auth token.")};const n={url:"https://labs.google/fx/api/auth/session",method:"GET",headers:new Headers({Cookie:this.credentials.cookie})},r=await gc(n);if(r.Err||!r.Ok)return{Err:r.Err};try{const l=JSON.parse(r.Ok);return l.access_token?(t&&(this.credentials.authorizationKey=l.access_token),{Ok:l.access_token}):{Err:Error("Access token is missing from response: "+r.Ok)}}catch{return{Err:Error("Failed to parse response: "+r.Ok)}}}async generateImage(t){const n=await Jo(this,ns,Dd).call(this);if(n.Err)return{Err:n.Err};t.model=="IMAGEN_4"&&(t.model="IMAGEN_3_5");const r={method:"POST",body:JSON.stringify({userInput:{candidatesCount:t.count||4,prompts:[t.prompt],seed:t.seed||0},aspectRatio:t.aspectRatio||"IMAGE_ASPECT_RATIO_LANDSCAPE",modelInput:{modelNameType:t.model||"IMAGEN_3_5"},clientContext:{sessionId:";1740658431200",tool:"IMAGE_FX"}}),url:"https://aisandbox-pa.googleapis.com/v1:runImageFx",headers:new Headers({Authorization:"Bearer "+this.credentials.authorizationKey})},l=await gc(r);if(l.Err||!l.Ok)return{Err:l.Err};try{const o=JSON.parse(l.Ok).imagePanels[0].generatedImages;return Array.isArray(o)?{Ok:o}:{Err:Error("Invalid response recieved: "+l.Ok)}}catch{return{Err:Error("Failed to parse JSON: "+l.Ok)}}}}ns=new WeakSet,Dd=async function(){return!this.credentials.authorizationKey&&!this.credentials.cookie?{Err:Error("Authorization token and Cookie both are missing.")}:(this.credentials.cookie&&!this.credentials.authorizationKey&&await this.getAuthToken(!0),{Ok:!0})};const Kp=["IMAGEN_2","IMAGEN_3","IMAGEN_4","IMAGEN_3_1","IMAGEN_3_5","IMAGEN_3_PORTRAIT","IMAGEN_3_LANDSCAPE","IMAGEN_3_PORTRAIT_THREE_FOUR","IMAGEN_3_LANDSCAPE_FOUR_THREE"];class Yp{constructor(t){ln(this,"fx",null);t&&(this.fx=new xc({authorizationKey:t}))}updateAuth(t){this.fx=new xc({authorizationKey:t})}async generateImages(t){if(!this.fx)throw new Error("ImageFX not initialized. Please provide authentication token.");try{const n=await this.fx.generateImage({prompt:t.prompt,count:t.count||4,model:t.model||"IMAGEN_4",aspectRatio:t.aspectRatio||"IMAGE_ASPECT_RATIO_LANDSCAPE",...t.seed!==void 0&&{seed:t.seed}});if(n.Err)throw new Error(n.Err.message||"ImageFX generation failed");if(!n.Ok||!Array.isArray(n.Ok))throw new Error("No images generated or invalid response format");return n.Ok.map((r,l)=>({id:`imagefx-${Date.now()}-${l}`,url:`data:image/png;base64,${r.encodedImage}`,prompt:t.prompt,timestamp:Date.now(),service:"imagefx"}))}catch(n){throw console.error("ImageFX generation error:",n),n instanceof Error?n:new Error("Unknown error occurred during ImageFX generation")}}isConfigured(){return this.fx!==null}}const Zp=[{value:"square",label:"Square (1:1)"},{value:"portrait",label:"Portrait (3:4)"},{value:"portrait_3_4",label:"Portrait (3:4) - ImageFX"},{value:"landscape",label:"Landscape (4:3)"},{value:"landscape_4_3",label:"Landscape (4:3) - ImageFX"}],qp=[{value:"512x512",label:"512 × 512"},{value:"768x768",label:"768 × 768"},{value:"1024x1024",label:"1024 × 1024"},{value:"1024x768",label:"1024 × 768"},{value:"768x1024",label:"768 × 1024"},{value:"1536x1024",label:"1536 × 1024"},{value:"1024x1536",label:"1024 × 1536"}],Jp=[{value:"flux",label:"Flux",description:"High-quality, balanced model"},{value:"turbo",label:"Turbo",description:"Fast generation, good quality"},{value:"flux-realism",label:"Flux Realism",description:"Photorealistic images"},{value:"flux-cablyai",label:"Flux CablyAI",description:"Artistic and creative"},{value:"flux-anime",label:"Flux Anime",description:"Anime and manga style"},{value:"flux-3d",label:"Flux 3D",description:"3D rendered style"},{value:"any-dark",label:"Any Dark",description:"Dark themed images"}],eh=[{value:"pollinations",label:"Pollinations AI",description:"Free, fast, no auth required"},{value:"imagefx",label:"ImageFX",description:"Google's ImageFX (requires auth)"},{value:"gpt4free",label:"GPT4Free",description:"Free unlimited AI image generation (no auth required)"}],th=({settings:e,onSettingsChange:t,onGenerate:n,isGenerating:r})=>{const l=e.prompt||"",a=u=>{u.preventDefault(),l.trim()&&!r&&n(l.trim())},o=u=>{c("prompt",u)},i=u=>{u.key==="Enter"&&(u.ctrlKey||u.metaKey)&&(u.preventDefault(),a(u))},c=(u,x)=>{t({...e,[u]:x})};return s.jsxs("div",{className:"w-80 bg-sidebar border-r border-matte flex flex-col relative",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-b from-purple-950/10 via-transparent to-transparent pointer-events-none"}),s.jsx("div",{className:"relative p-6 border-b border-matte/50",children:s.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[s.jsx("div",{className:"p-2 rounded-xl bg-gradient-to-br from-accent-primary to-accent-muted shadow-glow-sm",children:s.jsx(vn,{className:"w-5 h-5 text-white"})}),s.jsxs("div",{children:[s.jsx("h1",{className:"text-xl font-bold text-white tracking-tight",children:"Imagen AI"}),s.jsx("p",{className:"text-xs text-gray-400 font-medium",children:"AI Image Generator"})]})]})}),s.jsx("div",{className:"relative p-6 border-b border-matte/50",children:s.jsxs("form",{onSubmit:a,className:"space-y-5",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Describe your image"]}),s.jsx("span",{className:"text-xs text-gray-500 font-normal",children:"Ctrl+Enter to generate"})]}),s.jsxs("div",{className:"relative",children:[s.jsx("textarea",{value:l,onChange:u=>o(u.target.value),onKeyDown:i,placeholder:"A beautiful landscape with mountains and a lake at sunset...",className:"input-primary w-full h-28 resize-none text-sm leading-relaxed",disabled:r}),s.jsx("div",{className:"absolute inset-0 rounded-xl pointer-events-none bg-gradient-to-r from-transparent via-transparent to-accent-primary/5"})]}),s.jsxs("div",{className:"flex justify-between items-center mt-2",children:[s.jsx("div",{className:"text-xs text-gray-500",children:l.length>0&&`${l.length} characters`}),s.jsx("div",{className:"text-xs text-gray-500",children:l.length>500&&"Consider shortening for better results"})]})]}),s.jsx("button",{type:"submit",disabled:!l.trim()||r,className:"btn-primary w-full disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center gap-3 text-sm font-semibold",children:r?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Generating..."]}):s.jsxs(s.Fragment,{children:[s.jsx(vn,{className:"w-4 h-4"}),"Generate Images"]})})]})}),s.jsxs("div",{className:"relative p-6 flex-1 overflow-y-auto custom-scrollbar",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx("div",{className:"p-1.5 rounded-lg bg-gradient-to-br from-accent-primary/20 to-accent-muted/20 border border-accent-primary/30",children:s.jsx(Ad,{className:"w-4 h-4 text-accent-primary"})}),s.jsx("h2",{className:"text-lg font-bold text-white tracking-tight",children:"Settings"})]}),s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-4 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"AI Service"]}),s.jsx("div",{className:"space-y-3",children:eh.map(u=>s.jsxs("label",{className:"group flex items-start gap-4 p-4 rounded-xl border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300 hover:bg-card/50 backdrop-blur-sm",children:[s.jsxs("div",{className:"relative mt-0.5",children:[s.jsx("input",{type:"radio",name:"aiService",value:u.value,checked:e.aiService===u.value,onChange:x=>c("aiService",x.target.value),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded-full border-2 transition-all duration-300 ${e.aiService===u.value?"border-accent-primary bg-accent-primary shadow-glow-sm":"border-gray-500 group-hover:border-accent-primary/70"}`,children:e.aiService===u.value&&s.jsx("div",{className:"w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})})]}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"text-sm font-semibold text-white group-hover:text-accent-secondary transition-colors",children:u.label}),s.jsx("div",{className:"text-xs text-gray-400 mt-1 leading-relaxed",children:u.description})]})]},u.value))})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Aspect Ratio"]}),s.jsxs("div",{className:"relative",children:[s.jsx("select",{value:e.aspectRatio,onChange:u=>c("aspectRatio",u.target.value),className:"input-primary w-full appearance-none cursor-pointer",children:Zp.map(u=>s.jsx("option",{value:u.value,className:"bg-input text-white",children:u.label},u.value))}),s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none",children:s.jsx("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Resolution"]}),s.jsxs("div",{className:"relative",children:[s.jsx("select",{value:e.resolution,onChange:u=>c("resolution",u.target.value),className:"input-primary w-full appearance-none cursor-pointer",children:qp.map(u=>s.jsx("option",{value:u.value,className:"bg-input text-white",children:u.label},u.value))}),s.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none",children:s.jsx("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),e.aiService==="pollinations"&&s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Pollinations Model"]}),s.jsx("div",{className:"space-y-2",children:Jp.map(u=>s.jsxs("label",{className:"group flex items-start gap-4 p-3 rounded-xl border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300 hover:bg-card/50 backdrop-blur-sm",children:[s.jsxs("div",{className:"relative mt-0.5",children:[s.jsx("input",{type:"radio",name:"pollinationsModel",value:u.value,checked:e.pollinationsModel===u.value,onChange:x=>c("pollinationsModel",x.target.value),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded-full border-2 transition-all duration-300 ${e.pollinationsModel===u.value?"border-accent-primary bg-accent-primary shadow-glow-sm":"border-gray-500 group-hover:border-accent-primary/70"}`,children:e.pollinationsModel===u.value&&s.jsx("div",{className:"w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"})})]}),s.jsxs("div",{className:"flex-1",children:[s.jsx("div",{className:"text-sm font-semibold text-white group-hover:text-accent-secondary transition-colors",children:u.label}),s.jsx("div",{className:"text-xs text-gray-400 mt-1 leading-relaxed",children:u.description})]})]},u.value))})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Advanced Options"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-xs font-medium text-gray-300 mb-2 flex items-center gap-2",children:[s.jsx(mc,{className:"w-3 h-3"}),"Seed (optional)"]}),s.jsx("input",{type:"number",value:e.pollinationsSeed||"",onChange:u=>c("pollinationsSeed",u.target.value?parseInt(u.target.value):void 0),placeholder:"Random seed for reproducible results",className:"input-primary w-full text-sm",min:"0",max:"999999"})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[s.jsxs("label",{className:"flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300",children:[s.jsx("input",{type:"checkbox",checked:e.pollinationsNoLogo,onChange:u=>c("pollinationsNoLogo",u.target.checked),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${e.pollinationsNoLogo?"border-accent-primary bg-accent-primary":"border-gray-500"}`,children:e.pollinationsNoLogo&&s.jsx("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),s.jsxs("div",{children:[s.jsx("div",{className:"text-xs font-medium text-white",children:"No Logo"}),s.jsx("div",{className:"text-xs text-gray-400",children:"Remove watermark"})]})]}),s.jsxs("label",{className:"flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300",children:[s.jsx("input",{type:"checkbox",checked:e.pollinationsEnhance,onChange:u=>c("pollinationsEnhance",u.target.checked),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${e.pollinationsEnhance?"border-accent-primary bg-accent-primary":"border-gray-500"}`,children:e.pollinationsEnhance&&s.jsx("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-xs font-medium text-white flex items-center gap-1",children:[s.jsx(Gp,{className:"w-3 h-3"}),"Enhance"]}),s.jsx("div",{className:"text-xs text-gray-400",children:"Improve prompts"})]})]}),s.jsxs("label",{className:"flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300",children:[s.jsx("input",{type:"checkbox",checked:e.pollinationsSafe,onChange:u=>c("pollinationsSafe",u.target.checked),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${e.pollinationsSafe?"border-accent-primary bg-accent-primary":"border-gray-500"}`,children:e.pollinationsSafe&&s.jsx("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-xs font-medium text-white flex items-center gap-1",children:[s.jsx(Up,{className:"w-3 h-3"}),"Safe Mode"]}),s.jsx("div",{className:"text-xs text-gray-400",children:"Content filtering"})]})]}),s.jsxs("label",{className:"flex items-center gap-2 p-3 rounded-lg border border-matte hover:border-accent-primary/50 cursor-pointer transition-all duration-300",children:[s.jsx("input",{type:"checkbox",checked:e.pollinationsPrivate,onChange:u=>c("pollinationsPrivate",u.target.checked),className:"sr-only"}),s.jsx("div",{className:`w-4 h-4 rounded border-2 transition-all duration-300 flex items-center justify-center ${e.pollinationsPrivate?"border-accent-primary bg-accent-primary":"border-gray-500"}`,children:e.pollinationsPrivate&&s.jsx("svg",{className:"w-2 h-2 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-xs font-medium text-white flex items-center gap-1",children:[e.pollinationsPrivate?s.jsx(Ep,{className:"w-3 h-3"}):s.jsx(Ip,{className:"w-3 h-3"}),"Private"]}),s.jsx("div",{className:"text-xs text-gray-400",children:"Private generation"})]})]})]})]})]})]}),e.aiService==="imagefx"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"ImageFX Auth Token"}),s.jsx("input",{type:"password",value:e.imagefxAuth||"",onChange:u=>c("imagefxAuth",u.target.value),placeholder:"Enter your ImageFX authentication token",className:"input-primary w-full"}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Required for ImageFX service. See documentation for setup."})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"ImageFX Model"}),s.jsx("select",{value:e.imagefxModel||"IMAGEN_4",onChange:u=>c("imagefxModel",u.target.value),className:"input-primary w-full",children:Kp.map(u=>s.jsx("option",{value:u,children:u.replace(/_/g," ")},u))}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Choose the ImageFX model for generation."})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"text-sm font-semibold text-gray-200 mb-3 flex items-center gap-2",children:[s.jsx("div",{className:"w-1 h-4 bg-gradient-to-b from-accent-primary to-accent-secondary rounded-full"}),"Advanced Options"]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"text-xs font-medium text-gray-300 mb-2 flex items-center gap-2",children:[s.jsx(mc,{className:"w-3 h-3"}),"Seed (optional)"]}),s.jsx("input",{type:"number",value:e.imagefxSeed||"",onChange:u=>c("imagefxSeed",u.target.value?parseInt(u.target.value):void 0),placeholder:"Seed for reference image",className:"input-primary w-full text-sm",min:"0"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs font-medium text-gray-300 mb-2",children:"Number of Images"}),s.jsxs("select",{value:e.imagefxCount,onChange:u=>c("imagefxCount",parseInt(u.target.value)),className:"input-primary w-full text-sm",children:[s.jsx("option",{value:1,children:"1 image"}),s.jsx("option",{value:2,children:"2 images"}),s.jsx("option",{value:3,children:"3 images"}),s.jsx("option",{value:4,children:"4 images"}),s.jsx("option",{value:5,children:"5 images"}),s.jsx("option",{value:6,children:"6 images"}),s.jsx("option",{value:7,children:"7 images"}),s.jsx("option",{value:8,children:"8 images"})]}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"ImageFX supports generating 1-8 images per request."})]})]})]}),!e.imagefxAuth&&s.jsxs("div",{className:"flex items-start gap-2 p-3 bg-yellow-900/20 border border-yellow-600/30 rounded-lg",children:[s.jsx(Go,{className:"w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0"}),s.jsxs("div",{className:"text-xs text-yellow-200",children:[s.jsx("p",{className:"font-medium mb-1",children:"Authentication Required"}),s.jsx("p",{children:"ImageFX requires an authentication token to generate images. Please add your token above."})]})]})]}),e.aiService==="gpt4free"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"AI Model"}),s.jsxs("select",{value:e.gpt4freeModel||"flux",onChange:u=>c("gpt4freeModel",u.target.value),className:"input-primary w-full",children:[s.jsx("option",{value:"flux",children:"FLUX (Recommended)"}),s.jsx("option",{value:"dalle-3",children:"DALL-E 3"}),s.jsx("option",{value:"stable-diffusion-xl",children:"Stable Diffusion XL"}),s.jsx("option",{value:"playground-v2.5",children:"Playground v2.5"}),s.jsx("option",{value:"ideogram",children:"Ideogram"})]}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Choose the AI model for image generation. FLUX provides the best quality."})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Number of Images"}),s.jsx("input",{type:"number",min:"1",max:"8",value:e.gpt4freeCount||4,onChange:u=>c("gpt4freeCount",parseInt(u.target.value)),className:"input-primary w-full"}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Number of images to generate (1-8). More images take longer."})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Seed (Optional)"}),s.jsx("input",{type:"number",value:e.gpt4freeSeed||"",onChange:u=>c("gpt4freeSeed",u.target.value?parseInt(u.target.value):void 0),placeholder:"Random seed (leave empty for random)",className:"input-primary w-full"}),s.jsx("p",{className:"text-xs text-gray-400 mt-1",children:"Use a specific seed for reproducible results. Leave empty for random generation."})]}),s.jsxs("div",{className:"bg-green-900/20 rounded-lg p-4 border border-green-600/30",children:[s.jsx("h4",{className:"text-sm font-semibold text-green-200 mb-2",children:"✨ GPT4Free Features"}),s.jsxs("div",{className:"text-xs text-green-200 space-y-1",children:[s.jsxs("p",{children:["• ",s.jsx("strong",{children:"Completely Free:"})," No API keys or payments required"]}),s.jsxs("p",{children:["• ",s.jsx("strong",{children:"Unlimited Usage:"})," No rate limits or quotas"]}),s.jsxs("p",{children:["• ",s.jsx("strong",{children:"Multiple Models:"})," FLUX, DALL-E 3, Stable Diffusion XL"]}),s.jsxs("p",{children:["• ",s.jsx("strong",{children:"High Quality:"})," Professional-grade AI image generation"]}),s.jsxs("p",{children:["• ",s.jsx("strong",{children:"No Setup:"})," Works immediately without configuration"]})]})]})]})]})]})]})},nh=({isOpen:e,position:t,image:n,collections:r,onClose:l,onAddToCollection:a,onCreateNewCollection:o})=>{const i=N.useRef(null),[c,u]=N.useState(!1);if(N.useEffect(()=>{const U=d=>{i.current&&!i.current.contains(d.target)&&l()},f=d=>{d.key==="Escape"&&l()};return e&&(document.addEventListener("mousedown",U),document.addEventListener("keydown",f)),()=>{document.removeEventListener("mousedown",U),document.removeEventListener("keydown",f)}},[e,l]),!e||!n)return null;const x=U=>{c||(u(!0),a(U,n.id),setTimeout(()=>{u(!1),l()},300))},y=()=>{c||(u(!0),o([n.id]),setTimeout(()=>{u(!1),l()},300))},g=U=>U.imageIds.includes(n.id),j=250,w=Math.min(300,r.length*40+120),k={x:t.x+j>window.innerWidth?Math.max(10,t.x-j):t.x,y:t.y+w>window.innerHeight?Math.max(10,t.y-w):t.y};return s.jsxs("div",{ref:i,className:"fixed z-50 bg-gray-800 border border-gray-600 rounded-lg shadow-xl py-2 min-w-[200px]",style:{left:k.x,top:k.y},children:[s.jsxs("div",{className:"px-3 py-2 border-b border-gray-600",children:[s.jsx("p",{className:"text-sm font-medium text-white",children:"Add to Collection"}),s.jsx("p",{className:"text-xs text-gray-400 truncate",children:n.prompt})]}),s.jsx("div",{className:"max-h-60 overflow-y-auto",children:r.length>0?r.map(U=>{const f=g(U);return s.jsxs("button",{onClick:()=>!f&&!c&&x(U.id),disabled:f||c,className:`w-full px-3 py-2 text-left hover:bg-gray-700 transition-colors flex items-center gap-2 ${f||c?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:[s.jsx("div",{className:"w-3 h-3 rounded-full flex-shrink-0",style:{backgroundColor:U.color||"#3b82f6"}}),s.jsx(br,{className:"w-4 h-4 text-gray-400 flex-shrink-0"}),s.jsx("span",{className:"text-sm text-white truncate flex-1",children:U.name}),f&&s.jsx(Np,{className:"w-4 h-4 text-green-500 flex-shrink-0"})]},U.id)}):s.jsx("div",{className:"px-3 py-4 text-center",children:s.jsx("p",{className:"text-sm text-gray-400",children:"No collections yet"})})}),s.jsx("div",{className:"border-t border-gray-600 mt-2",children:s.jsxs("button",{onClick:y,disabled:c,className:`w-full px-3 py-2 text-left hover:bg-gray-700 transition-colors flex items-center gap-2 text-accent-primary ${c?"opacity-50 cursor-not-allowed":""}`,children:[c?s.jsx(_p,{className:"w-4 h-4 animate-spin"}):s.jsx(Xo,{className:"w-4 h-4"}),s.jsx("span",{className:"text-sm",children:c?"Creating...":"Create New Collection"})]})})]})},na=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#ec4899","#06b6d4","#84cc16","#f97316","#6366f1"],rh=({isOpen:e,onClose:t,onCreateCollection:n,imagesToAdd:r,previewImages:l=[]})=>{const[a,o]=N.useState(""),[i,c]=N.useState(""),[u,x]=N.useState(na[0]),y=N.useRef(null);N.useEffect(()=>{e&&(o(""),c(""),x(na[0]),setTimeout(()=>{var w;(w=y.current)==null||w.focus()},100))},[e]),N.useEffect(()=>{const w=k=>{k.key==="Escape"&&t()};return e&&(document.addEventListener("keydown",w),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",w),document.body.style.overflow="unset"}},[e,t]);const g=w=>{w.preventDefault(),a.trim()&&(n({name:a.trim(),description:i.trim()||void 0,imageIds:r,color:u}),t())},j=w=>{w.key==="Enter"&&(w.ctrlKey||w.metaKey)&&g(w)};return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-gray-800 rounded-lg border border-gray-600 w-full max-w-md mx-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-600",children:[s.jsx("h3",{className:"text-lg font-semibold text-white",children:"Create Collection"}),s.jsx("button",{onClick:t,className:"p-1 text-gray-400 hover:text-white transition-colors",children:s.jsx(yt,{className:"w-5 h-5"})})]}),s.jsxs("form",{onSubmit:g,className:"p-4",children:[s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Collection Name *"}),s.jsx("input",{ref:y,type:"text",value:a,onChange:w=>o(w.target.value),onKeyDown:j,placeholder:"Enter collection name...",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none",maxLength:50})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),s.jsx("textarea",{value:i,onChange:w=>c(w.target.value),onKeyDown:j,placeholder:"Enter description...",rows:2,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none",maxLength:200})]}),s.jsxs("div",{className:"mb-4",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Color"}),s.jsx("div",{className:"flex items-center gap-2 flex-wrap",children:na.map(w=>s.jsx("button",{type:"button",onClick:()=>x(w),className:`w-8 h-8 rounded-full border-2 transition-all ${u===w?"border-white scale-110":"border-gray-600 hover:border-gray-400"}`,style:{backgroundColor:w}},w))})]}),l.length>0&&s.jsxs("div",{className:"mb-4",children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Images to Add (",r.length,")"]}),s.jsxs("div",{className:"flex gap-2 overflow-x-auto pb-2",children:[l.slice(0,5).map(w=>s.jsx("div",{className:"flex-shrink-0",children:s.jsx("img",{src:w.url,alt:w.prompt,className:"w-12 h-12 object-cover rounded border border-gray-600"})},w.id)),l.length>5&&s.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-gray-700 rounded border border-gray-600 flex items-center justify-center",children:s.jsxs("span",{className:"text-xs text-gray-400",children:["+",l.length-5]})})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsxs("button",{type:"submit",disabled:!a.trim(),className:"flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors",children:[s.jsx(ts,{className:"w-4 h-4"}),"Create Collection"]}),s.jsx("button",{type:"button",onClick:t,className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors",children:"Cancel"})]}),s.jsx("p",{className:"text-xs text-gray-500 mt-2",children:"Press Ctrl+Enter to create quickly"})]})]})}):null},lh=({image:e,onDownload:t,onDelete:n,onPreview:r,onEdit:l,isSelected:a,isSelectionMode:o,onToggleSelection:i,onRightClick:c,collections:u=[]})=>{const[x,y]=N.useState(!1),[g,j]=N.useState(0),w=p=>new Date(p).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),k=u.filter(p=>!p.isDefault&&p.imageIds.includes(e.id));if(e.isLoading)return s.jsxs("div",{className:"bg-card rounded-2xl border border-matte overflow-hidden backdrop-blur-sm animate-fade-in",children:[s.jsxs("div",{className:"aspect-square bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center relative",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-accent-primary/10 to-accent-muted/5"}),s.jsxs("div",{className:"text-center relative z-10",children:[s.jsx("div",{className:"w-10 h-10 border-4 border-accent-primary border-t-transparent rounded-full animate-spin mx-auto mb-3 shadow-glow-sm"}),s.jsx("p",{className:"text-sm text-gray-300 font-medium",children:"Generating..."}),s.jsx("div",{className:"w-16 h-1 bg-gray-700 rounded-full mt-3 mx-auto overflow-hidden",children:s.jsx("div",{className:"h-full bg-gradient-to-r from-accent-primary to-accent-secondary rounded-full animate-pulse-glow"})})]})]}),s.jsxs("div",{className:"p-4",children:[s.jsx("p",{className:"text-sm text-gray-300 line-clamp-2 leading-relaxed",children:e.prompt}),s.jsxs("div",{className:"flex items-center gap-3 mt-3 text-xs text-gray-400",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(es,{className:"w-3 h-3"}),s.jsx("span",{children:w(e.timestamp)})]}),s.jsx("div",{className:"w-1 h-1 bg-gray-600 rounded-full"}),s.jsx("span",{className:"capitalize font-medium",children:e.service})]})]})]});const U=()=>{o&&i?i(e.id):r(e)},f=p=>{p.preventDefault(),c&&c(e,{x:p.clientX,y:p.clientY})},d=()=>{y(!0)},m=()=>{if(g<3){y(!1),j(b=>b+1);const p=new Image;p.onload=()=>y(!1),p.onerror=()=>y(!0),p.src=`${e.url}?retry=${g+1}`}};return s.jsxs("div",{className:`bg-card rounded-2xl border overflow-hidden group hover:border-accent-primary/50 transition-all duration-500 hover:shadow-glow-sm backdrop-blur-sm animate-fade-in cursor-pointer ${a?"border-accent-primary shadow-glow-sm ring-2 ring-accent-primary/30":"border-matte"}`,onClick:U,onContextMenu:f,children:[s.jsxs("div",{className:"relative aspect-square overflow-hidden",children:[x?s.jsx("div",{className:"w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center",children:s.jsxs("div",{className:"text-center p-4",children:[s.jsx("div",{className:"w-12 h-12 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx(Go,{className:"w-6 h-6 text-red-400"})}),s.jsx("p",{className:"text-sm text-gray-300 mb-3",children:"Failed to load image"}),g<3&&s.jsxs("button",{onClick:p=>{p.stopPropagation(),m()},className:"px-3 py-1 bg-accent-primary hover:bg-accent-secondary text-white text-xs rounded-lg transition-colors",children:["Retry (",3-g," left)"]})]})}):s.jsx("img",{src:e.url,alt:e.prompt,className:"w-full h-full object-cover transition-all duration-500 group-hover:scale-110",loading:"lazy",onError:d}),s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),s.jsx("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none z-10",children:s.jsx("div",{className:"bg-black/70 backdrop-blur-sm rounded-full px-3 py-2 border border-white/20",children:s.jsx("div",{className:"text-white text-xs font-medium",children:"Click to preview"})})}),s.jsx("div",{className:"absolute bottom-3 right-3 opacity-0 group-hover:opacity-100 transition-all duration-300 z-20",children:s.jsxs("div",{className:"flex items-center gap-2",children:[l&&!e.isLoading&&s.jsx("button",{onClick:p=>{p.stopPropagation(),l(e)},className:"w-8 h-8 bg-purple-600 hover:bg-purple-700 text-white rounded-lg flex items-center justify-center shadow-xl transition-all duration-200",title:"Edit",children:s.jsx(Qr,{className:"w-4 h-4"})}),s.jsx("button",{onClick:p=>{p.stopPropagation(),t(e)},className:"w-8 h-8 bg-accent-primary hover:bg-accent-secondary text-white rounded-lg flex items-center justify-center shadow-xl transition-all duration-200",title:"Download",children:s.jsx(Xr,{className:"w-4 h-4"})}),!e.isLoading&&s.jsx("button",{onClick:p=>{p.stopPropagation(),n(e)},className:"w-8 h-8 bg-red-600 hover:bg-red-700 text-white rounded-lg flex items-center justify-center shadow-xl border border-red-500/50 transition-all duration-200",title:"Delete",children:s.jsx(Nn,{className:"w-4 h-4"})})]})}),o&&s.jsx("div",{className:"absolute top-3 left-3 z-30",children:s.jsxs("div",{className:`w-6 h-6 rounded border-2 flex items-center justify-center transition-all duration-200 ${a?"bg-accent-primary border-accent-primary text-white":"bg-black/50 border-white/30 backdrop-blur-sm"}`,children:[a&&s.jsx(jp,{className:"w-4 h-4"}),!a&&s.jsx(Td,{className:"w-4 h-4"})]})}),s.jsx("div",{className:`absolute top-3 right-3 transition-opacity duration-300 ${o?"opacity-100":"opacity-0 group-hover:opacity-100"}`,children:s.jsx("div",{className:"px-2 py-1 bg-black/50 backdrop-blur-sm rounded-lg border border-white/10",children:s.jsxs("div",{className:"flex items-center gap-1 text-xs text-white",children:[s.jsx(vn,{className:"w-3 h-3"}),s.jsx("span",{className:"capitalize font-medium",children:e.service})]})})}),k.length>0&&s.jsx("div",{className:"absolute bottom-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:s.jsxs("div",{className:"flex items-center gap-1",children:[k.slice(0,3).map(p=>s.jsx("div",{className:"w-3 h-3 rounded-full border border-white/30",style:{backgroundColor:p.color||"#3b82f6"},title:p.name},p.id)),k.length>3&&s.jsx("div",{className:"w-3 h-3 rounded-full bg-gray-600 border border-white/30 flex items-center justify-center",children:s.jsx("span",{className:"text-[8px] text-white font-bold",children:"+"})})]})})]}),s.jsxs("div",{className:"p-4",children:[s.jsx("p",{className:"text-sm text-gray-300 line-clamp-2 mb-3 leading-relaxed",children:e.prompt}),s.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-400",children:[s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx(es,{className:"w-3 h-3"}),s.jsx("span",{children:w(e.timestamp)})]}),s.jsx("div",{className:"w-1 h-1 bg-gray-600 rounded-full"}),s.jsx("span",{className:"capitalize font-medium",children:e.service})]})]})]})},sh=({images:e,totalImages:t,onDownload:n,onDelete:r,onPreview:l,onRefresh:a,onEdit:o,selectedImages:i=new Set,isSelectionMode:c=!1,onToggleSelection:u,onToggleSelectionMode:x,onSelectAll:y,onDeselectAll:g,onBatchDownload:j,onBatchDelete:w,onBatchExport:k,searchQuery:U="",onSearchChange:f,filterService:d="all",onFilterServiceChange:m,sortBy:p="newest",onSortChange:b,dateFilter:M="all",onDateFilterChange:F,onOpenComparison:E,onOpenCollections:B,collections:L=[],onAddToCollection:q,onCreateCollection:ve})=>{const[G,ae]=N.useState({isOpen:!1,position:{x:0,y:0},image:null}),[ie,ge]=N.useState({isOpen:!1,imagesToAdd:[]}),ct=(_,A)=>{ae({isOpen:!0,position:A,image:_})},O=()=>{ae({isOpen:!1,position:{x:0,y:0},image:null})},$=(_,A)=>{q&&q(_,A)},D=_=>{ge({isOpen:!0,imagesToAdd:_}),O()},ne=()=>{ge({isOpen:!1,imagesToAdd:[]})},Z=_=>{ve&&ve(_)},we=(t||0)>0,C=U||d!=="all"||M!=="all";return e.length===0?s.jsxs("div",{className:"h-full w-full flex items-center justify-center bg-app relative",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-accent-primary/5 via-transparent to-accent-muted/5"}),s.jsxs("div",{className:"text-center relative z-10 max-w-md mx-auto px-6",children:[s.jsxs("div",{className:"relative mb-6",children:[s.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-accent-primary to-accent-muted rounded-full blur-xl opacity-30 animate-pulse-glow"}),we&&C?s.jsx(fc,{className:"w-20 h-20 text-accent-primary mx-auto relative z-10"}):s.jsx(vn,{className:"w-20 h-20 text-accent-primary mx-auto relative z-10"})]}),we&&C?s.jsxs(s.Fragment,{children:[s.jsx("h2",{className:"text-2xl font-bold text-white mb-3 tracking-tight",children:"No Results Found"}),s.jsx("p",{className:"text-gray-400 leading-relaxed",children:"No images match your current search and filter criteria. Try adjusting your filters or search terms."}),s.jsxs("div",{className:"mt-8 flex flex-col items-center gap-4",children:[s.jsxs("div",{className:"flex items-center gap-2 px-4 py-2 bg-card/50 backdrop-blur-sm rounded-xl border border-matte",children:[s.jsx("div",{className:"w-2 h-2 bg-yellow-500 rounded-full animate-pulse"}),s.jsx("span",{className:"text-xs text-gray-400 font-medium",children:"Try different search terms..."})]}),s.jsxs("button",{onClick:()=>{f==null||f(""),m==null||m("all"),F==null||F("all")},className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200",children:[s.jsx(yt,{className:"w-4 h-4"}),s.jsx("span",{className:"text-sm",children:"Clear Filters"})]})]})]}):s.jsxs(s.Fragment,{children:[s.jsx("h2",{className:"text-2xl font-bold text-white mb-3 tracking-tight",children:"Ready to Create"}),s.jsx("p",{className:"text-gray-400 leading-relaxed",children:'Enter a prompt in the sidebar and click "Generate Images" to start creating amazing AI-generated artwork.'}),s.jsxs("div",{className:"mt-8 flex flex-col items-center gap-4",children:[s.jsxs("div",{className:"flex items-center gap-2 px-4 py-2 bg-card/50 backdrop-blur-sm rounded-xl border border-matte",children:[s.jsx("div",{className:"w-2 h-2 bg-accent-primary rounded-full animate-pulse"}),s.jsx("span",{className:"text-xs text-gray-400 font-medium",children:"Waiting for your creativity..."})]}),a&&s.jsxs("button",{onClick:a,className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200",children:[s.jsx(Ya,{className:"w-4 h-4"}),s.jsx("span",{className:"text-sm",children:"Refresh Gallery"})]})]})]})]})]}):s.jsxs("div",{className:"h-full w-full overflow-y-auto bg-app relative custom-scrollbar",children:[s.jsx("div",{className:"absolute inset-0 opacity-5",children:s.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-accent-primary/10 via-transparent to-accent-muted/10"})}),s.jsxs("div",{className:"relative z-10 p-8",children:[s.jsxs("div",{className:"mb-6 space-y-4",children:[s.jsxs("div",{className:"relative",children:[s.jsx(fc,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),s.jsx("input",{type:"text",placeholder:"Search images by prompt or service...",value:U,onChange:_=>f==null?void 0:f(_.target.value),className:"w-full pl-10 pr-4 py-3 bg-card border border-matte rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none transition-colors"})]}),s.jsxs("div",{className:"flex flex-wrap items-center gap-4",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Md,{className:"w-4 h-4 text-gray-400"}),s.jsxs("select",{value:d,onChange:_=>m==null?void 0:m(_.target.value),className:"bg-card border border-matte rounded-lg px-3 py-2 text-white text-sm focus:border-accent-primary focus:outline-none",children:[s.jsx("option",{value:"all",children:"All Services"}),s.jsx("option",{value:"pollinations",children:"Pollinations"}),s.jsx("option",{value:"imagefx",children:"ImageFX"}),s.jsx("option",{value:"gpt4free",children:"GPT4Free"})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(es,{className:"w-4 h-4 text-gray-400"}),s.jsxs("select",{value:M,onChange:_=>F==null?void 0:F(_.target.value),className:"bg-card border border-matte rounded-lg px-3 py-2 text-white text-sm focus:border-accent-primary focus:outline-none",children:[s.jsx("option",{value:"all",children:"All Time"}),s.jsx("option",{value:"today",children:"Today"}),s.jsx("option",{value:"week",children:"This Week"}),s.jsx("option",{value:"month",children:"This Month"})]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(vp,{className:"w-4 h-4 text-gray-400"}),s.jsxs("select",{value:p,onChange:_=>b==null?void 0:b(_.target.value),className:"bg-card border border-matte rounded-lg px-3 py-2 text-white text-sm focus:border-accent-primary focus:outline-none",children:[s.jsx("option",{value:"newest",children:"Newest First"}),s.jsx("option",{value:"oldest",children:"Oldest First"}),s.jsx("option",{value:"prompt",children:"By Prompt (A-Z)"})]})]}),s.jsx("div",{className:"ml-auto text-sm text-gray-400",children:e.length===(t||e.length)?`${e.length} image${e.length!==1?"s":""}`:`${e.length} of ${t||e.length} image${(t||e.length)!==1?"s":""}`})]})]}),s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx("div",{className:"flex items-center gap-4",children:s.jsxs("h2",{className:"text-xl font-bold text-white",children:["Generated Images",c&&i.size>0&&s.jsxs("span",{className:"text-accent-primary ml-2",children:["(",i.size," selected)"]})]})}),s.jsxs("div",{className:"flex items-center gap-2",children:[c&&s.jsxs(s.Fragment,{children:[s.jsx("button",{onClick:y,className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200 text-sm",children:"Select All"}),s.jsx("button",{onClick:g,className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200 text-sm",children:"Deselect All"}),i.size>0&&s.jsxs(s.Fragment,{children:[s.jsxs("button",{onClick:()=>D(Array.from(i)),className:"flex items-center gap-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(Xo,{className:"w-4 h-4"}),"Add to Collection (",i.size,")"]}),s.jsxs("button",{onClick:j,className:"flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(Pp,{className:"w-4 h-4"}),"Download (",i.size,")"]}),s.jsxs("button",{onClick:k,className:"flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(yp,{className:"w-4 h-4"}),"Export (",i.size,")"]}),s.jsxs("button",{onClick:w,className:"flex items-center gap-2 px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(Nn,{className:"w-4 h-4"}),"Delete (",i.size,")"]})]})]}),x&&s.jsxs("button",{onClick:x,className:`flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200 text-sm ${c?"bg-accent-primary hover:bg-accent-secondary text-white border-accent-primary":"bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white border-matte hover:border-accent-primary/50"}`,children:[s.jsx(Dp,{className:"w-4 h-4"}),c?"Exit Selection":"Select"]}),B&&s.jsxs("button",{onClick:B,className:"flex items-center gap-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(br,{className:"w-4 h-4"}),"Collections"]}),E&&e.length>=2&&s.jsxs("button",{onClick:E,className:"flex items-center gap-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all duration-200 text-sm",children:[s.jsx(zp,{className:"w-4 h-4"}),"Compare"]}),a&&s.jsxs("button",{onClick:a,className:"flex items-center gap-2 px-3 py-2 bg-card/30 hover:bg-card/50 text-gray-400 hover:text-white rounded-lg border border-matte hover:border-accent-primary/50 transition-all duration-200 text-sm",children:[s.jsx(Ya,{className:"w-4 h-4"}),"Refresh"]})]})]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map((_,A)=>s.jsx("div",{style:{animationDelay:`${A*100}ms`},className:"animate-slide-up",children:s.jsx(lh,{image:_,onDownload:n,onDelete:r,onPreview:l,onEdit:o,isSelected:i.has(_.id),isSelectionMode:c,onToggleSelection:u,onRightClick:ct,collections:L})},_.id))})]}),s.jsx(nh,{isOpen:G.isOpen,position:G.position,image:G.image,collections:L,onClose:O,onAddToCollection:$,onCreateNewCollection:D}),s.jsx(rh,{isOpen:ie.isOpen,onClose:ne,onCreateCollection:Z,imagesToAdd:ie.imagesToAdd,previewImages:e.filter(_=>ie.imagesToAdd.includes(_.id))})]})},ah=({image:e,isOpen:t,onClose:n,onDownload:r,onDelete:l,onEdit:a})=>{if(N.useEffect(()=>{const c=u=>{u.key==="Escape"&&n()};return t&&(document.addEventListener("keydown",c),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",c),document.body.style.overflow="unset"}},[t,n]),!t||!e)return null;const o=c=>new Date(c).toLocaleString(),i=()=>{const c={"512x512":"512 × 512","768x768":"768 × 768","1024x1024":"1024 × 1024","1024x768":"1024 × 768","768x1024":"768 × 1024","1536x1024":"1536 × 1024","1024x1536":"1024 × 1536"};for(const[u,x]of Object.entries(c))if(e.url.includes(u.replace("x","%2C")))return x;return"Unknown"};return s.jsxs("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[s.jsx("div",{className:"absolute inset-0 bg-black/80 backdrop-blur-md",onClick:n}),s.jsxs("div",{className:"relative w-full max-w-6xl max-h-[90vh] mx-4 bg-card rounded-3xl border border-matte shadow-2xl overflow-hidden animate-fade-in",children:[s.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-matte bg-gradient-to-r from-card to-card/80",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx("div",{className:"w-8 h-8 rounded-xl bg-gradient-to-br from-accent-primary to-accent-secondary flex items-center justify-center",children:s.jsx(Wn,{className:"w-4 h-4 text-white"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-xl font-bold text-white",children:"Generated Image Preview"}),s.jsx("p",{className:"text-sm text-gray-400",children:"Click outside or press Escape to close"})]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[a&&s.jsxs("button",{onClick:()=>{a(e),n()},className:"bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-200",children:[s.jsx(Qr,{className:"w-4 h-4"}),"Edit"]}),s.jsxs("button",{onClick:()=>r(e),className:"btn-primary flex items-center gap-2 px-4 py-2",children:[s.jsx(Xr,{className:"w-4 h-4"}),"Download"]}),s.jsxs("button",{onClick:()=>{l(e),n()},className:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-200 border border-red-500/50",children:[s.jsx(Nn,{className:"w-4 h-4"}),"Delete"]}),s.jsx("button",{onClick:n,className:"p-2 rounded-lg hover:bg-matte transition-colors text-gray-400 hover:text-white",children:s.jsx(yt,{className:"w-5 h-5"})})]})]}),s.jsxs("div",{className:"flex flex-col lg:flex-row max-h-[calc(90vh-80px)]",children:[s.jsx("div",{className:"flex-1 p-6 flex items-center justify-center bg-gradient-to-br from-gray-900/50 to-gray-800/50",children:s.jsx("div",{className:"relative max-w-full max-h-full",children:s.jsx("img",{src:e.url,alt:e.prompt,className:"max-w-full max-h-[60vh] object-contain rounded-2xl shadow-2xl",loading:"lazy"})})}),s.jsxs("div",{className:"w-full lg:w-96 border-l border-matte bg-card/50 overflow-y-auto custom-scrollbar",children:[s.jsxs("div",{className:"p-6 border-b border-matte",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"w-6 h-6 rounded-lg bg-gradient-to-br from-accent-primary to-accent-secondary flex items-center justify-center",children:s.jsx(_d,{className:"w-3 h-3 text-white"})}),s.jsx("h3",{className:"text-lg font-bold text-white",children:"Prompt Details"})]}),s.jsx("div",{className:"space-y-3",children:s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs font-medium text-gray-400 mb-2",children:"Generated Prompt"}),s.jsx("div",{className:"bg-input rounded-xl p-4 border border-matte",children:s.jsx("p",{className:"text-sm text-gray-300 leading-relaxed",children:e.prompt})})]})})]}),s.jsxs("div",{className:"p-6",children:[s.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[s.jsx("div",{className:"w-6 h-6 rounded-lg bg-gradient-to-br from-accent-primary to-accent-secondary flex items-center justify-center",children:s.jsx(Ad,{className:"w-3 h-3 text-white"})}),s.jsx("h3",{className:"text-lg font-bold text-white",children:"Image Details"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(vn,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Service"})]}),s.jsx("p",{className:"text-sm font-semibold text-white capitalize",children:e.service})]}),s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(Wn,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Dimensions"})]}),s.jsx("p",{className:"text-sm font-semibold text-white",children:i()})]})]}),s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(es,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Generated"})]}),s.jsx("p",{className:"text-sm font-semibold text-white",children:o(e.timestamp)})]}),s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(Mp,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Image ID"})]}),s.jsx("p",{className:"text-xs font-mono text-gray-300 break-all",children:e.id})]}),e.filename&&s.jsxs("div",{className:"bg-input rounded-xl p-3 border border-matte",children:[s.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[s.jsx(Wn,{className:"w-3 h-3 text-accent-primary"}),s.jsx("span",{className:"text-xs font-medium text-gray-400",children:"Filename"})]}),s.jsx("p",{className:"text-xs font-mono text-gray-300 break-all",children:e.filename})]})]})]})]})]})]})]})},oh=({isOpen:e,onToggle:t,onPromptSelect:n})=>{const[r,l]=N.useState("library"),[a,o]=N.useState([]),[i,c]=N.useState(null),[u,x]=N.useState(""),[y,g]=N.useState(""),[j,w]=N.useState(""),[k,U]=N.useState(""),[f,d]=N.useState(!1);if(N.useEffect(()=>{(async()=>{if(window.electronAPI)try{const L=await window.electronAPI.loadPrompts();L.success&&L.prompts&&o(L.prompts)}catch(L){console.error("Failed to load prompts:",L)}})()},[]),N.useEffect(()=>{(async()=>{if(window.electronAPI&&a.length>0)try{await window.electronAPI.savePrompts(a)}catch(L){console.error("Failed to save prompts:",L)}})()},[a]),!e)return s.jsx("button",{onClick:t,className:"fixed top-4 right-4 z-50 w-10 h-10 bg-black/20 hover:bg-black/40 backdrop-blur-sm border border-white/10 rounded-lg flex items-center justify-center transition-all duration-300 hover:scale-105",title:"Open Prompt Tools",children:s.jsx(kp,{className:"w-5 h-5 text-white rotate-180"})});const m=()=>{if(!u.trim()||!y.trim())return;const B={id:Date.now().toString(),title:u.trim(),prompt:y.trim(),createdAt:Date.now(),updatedAt:Date.now()};o(L=>[B,...L]),x(""),g("")},p=B=>{c(B),x(B.title),g(B.prompt)},b=()=>{!i||!u.trim()||!y.trim()||(o(B=>B.map(L=>L.id===i.id?{...L,title:u.trim(),prompt:y.trim(),updatedAt:Date.now()}:L)),c(null),x(""),g(""))},M=B=>{o(L=>L.filter(q=>q.id!==B))},F=async()=>{if(j.trim()){d(!0),U("");try{const B=`Enhance this image generation prompt to be more detailed, creative, and effective for AI image generation. Make it more descriptive and artistic while keeping the core concept. Original prompt: "${j.trim()}". Return only the enhanced prompt without any additional text, quotes, or explanation.`,L=await fetch("https://text.pollinations.ai/",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({messages:[{role:"user",content:B}],model:"openai"})});if(L.ok){const ve=(await L.text()).trim().replace(/^["']|["']$/g,"");U(ve||"Enhancement completed but result was empty. Please try again.")}else throw new Error(`HTTP ${L.status}: ${L.statusText}`)}catch(B){console.error("Prompt enhancement failed:",B),U("Failed to enhance prompt. Please check your internet connection and try again.")}finally{d(!1)}}},E=()=>{k&&n(k)};return s.jsxs("div",{className:"fixed top-0 right-0 h-full w-96 max-w-[90vw] bg-card border-l border-matte shadow-2xl z-40 flex flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-matte bg-gradient-to-r from-card to-card/80",children:[s.jsx("h2",{className:"text-lg font-bold text-white",children:"Prompt Tools"}),s.jsx("button",{onClick:t,className:"p-2 rounded-lg hover:bg-matte transition-colors text-gray-400 hover:text-white",children:s.jsx(yt,{className:"w-5 h-5"})})]}),s.jsxs("div",{className:"flex border-b border-matte",children:[s.jsx("button",{onClick:()=>l("library"),className:`flex-1 px-4 py-3 text-sm font-medium transition-colors ${r==="library"?"text-accent-primary border-b-2 border-accent-primary bg-accent-primary/5":"text-gray-400 hover:text-white hover:bg-matte/50"}`,children:s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx(cc,{className:"w-4 h-4"}),"Library"]})}),s.jsx("button",{onClick:()=>l("enhancer"),className:`flex-1 px-4 py-3 text-sm font-medium transition-colors ${r==="enhancer"?"text-accent-primary border-b-2 border-accent-primary bg-accent-primary/5":"text-gray-400 hover:text-white hover:bg-matte/50"}`,children:s.jsxs("div",{className:"flex items-center justify-center gap-2",children:[s.jsx(hc,{className:"w-4 h-4"}),"Enhancer"]})})]}),s.jsxs("div",{className:"flex-1 overflow-y-auto custom-scrollbar",children:[r==="library"&&s.jsxs("div",{className:"p-4 space-y-4",children:[s.jsxs("div",{className:"bg-input rounded-xl p-4 border border-matte",children:[s.jsxs("h3",{className:"text-sm font-semibold text-white mb-3 flex items-center gap-2",children:[s.jsx(mn,{className:"w-4 h-4"}),i?"Edit Prompt":"Add New Prompt"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("input",{type:"text",placeholder:"Prompt title...",value:u,onChange:B=>x(B.target.value),className:"w-full px-3 py-2 bg-card border border-matte rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none"}),s.jsx("textarea",{placeholder:"Enter your prompt...",value:y,onChange:B=>g(B.target.value),rows:3,className:"w-full px-3 py-2 bg-card border border-matte rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none"}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:i?b:m,disabled:!u.trim()||!y.trim(),className:"flex-1 btn-primary flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:[s.jsx(ts,{className:"w-4 h-4"}),i?"Update":"Save"]}),i&&s.jsx("button",{onClick:()=>{c(null),x(""),g("")},className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",children:"Cancel"})]})]})]}),s.jsx("div",{className:"space-y-3",children:a.length===0?s.jsxs("div",{className:"text-center py-8 text-gray-400",children:[s.jsx(cc,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),s.jsx("p",{className:"text-sm",children:"No saved prompts yet"}),s.jsx("p",{className:"text-xs mt-1",children:"Add your first prompt above"})]}):a.map(B=>s.jsxs("div",{className:"bg-input rounded-xl p-4 border border-matte hover:border-accent-primary/50 transition-colors",children:[s.jsxs("div",{className:"flex items-start justify-between mb-2",children:[s.jsx("h4",{className:"font-medium text-white text-sm",children:B.title}),s.jsxs("div",{className:"flex gap-1",children:[s.jsx("button",{onClick:()=>p(B),className:"p-1 rounded hover:bg-matte text-gray-400 hover:text-white transition-colors",title:"Edit",children:s.jsx(Qr,{className:"w-3 h-3"})}),s.jsx("button",{onClick:()=>M(B.id),className:"p-1 rounded hover:bg-red-600/20 text-gray-400 hover:text-red-400 transition-colors",title:"Delete",children:s.jsx(Nn,{className:"w-3 h-3"})})]})]}),s.jsx("p",{className:"text-xs text-gray-300 mb-3 line-clamp-3",children:B.prompt}),s.jsx("button",{onClick:()=>n(B.prompt),className:"w-full px-3 py-2 bg-accent-primary/20 hover:bg-accent-primary/30 text-accent-primary rounded-lg text-xs font-medium transition-colors",children:"Use This Prompt"})]},B.id))})]}),r==="enhancer"&&s.jsxs("div",{className:"p-4 space-y-4",children:[s.jsxs("div",{className:"bg-input rounded-xl p-4 border border-matte",children:[s.jsxs("h3",{className:"text-sm font-semibold text-white mb-3 flex items-center gap-2",children:[s.jsx(vn,{className:"w-4 h-4"}),"AI Prompt Enhancer"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("textarea",{placeholder:"Enter your basic prompt here...",value:j,onChange:B=>w(B.target.value),rows:4,className:"w-full px-3 py-2 bg-card border border-matte rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none"}),s.jsx("button",{onClick:F,disabled:!j.trim()||f,className:"w-full btn-primary flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed",children:f?s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"Enhancing..."]}):s.jsxs(s.Fragment,{children:[s.jsx(hc,{className:"w-4 h-4"}),"Enhance Prompt"]})})]})]}),k&&s.jsxs("div",{className:"bg-input rounded-xl p-4 border border-matte",children:[s.jsxs("h4",{className:"text-sm font-semibold text-white mb-3 flex items-center gap-2",children:[s.jsx(vn,{className:"w-4 h-4 text-accent-primary"}),"Enhanced Prompt"]}),s.jsxs("div",{className:"space-y-3",children:[s.jsx("div",{className:"p-3 bg-card rounded-lg border border-matte",children:s.jsx("p",{className:"text-sm text-gray-300 leading-relaxed",children:k})}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:E,className:"flex-1 btn-primary flex items-center justify-center gap-2",children:[s.jsx(mn,{className:"w-4 h-4"}),"Use Enhanced Prompt"]}),s.jsx("button",{onClick:()=>{x("Enhanced: "+j.slice(0,30)+"..."),g(k),l("library")},className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors flex items-center gap-2",title:"Save to Library",children:s.jsx(ts,{className:"w-4 h-4"})})]})]})]})]})]})]})},xl={brightness:100,contrast:100,saturation:100,hue:0,blur:0,rotation:0,flipX:!1,flipY:!1,cropX:0,cropY:0,cropWidth:100,cropHeight:100,zoom:1,panX:0,panY:0,shadows:0,highlights:0,temperature:0,tint:0,exposure:0,textOverlays:[],drawings:[]},ih=({image:e,isOpen:t,onClose:n,onSave:r})=>{var Zr,qr;const[l,a]=N.useState(xl),[o,i]=N.useState([xl]),[c,u]=N.useState(0),[x,y]=N.useState("adjust"),[g,j]=N.useState(!1),[w,k]=N.useState({x:0,y:0}),[U,f]=N.useState(!1),[d,m]=N.useState({x:0,y:0,width:0,height:0}),[p,b]=N.useState(null),[M,F]=N.useState(null),[E,B]=N.useState(null),[L,q]=N.useState("#ffffff"),[ve,G]=N.useState(3),[ae,ie]=N.useState(1),[ge,ct]=N.useState(!1),[O,$]=N.useState(["#ff0000","#ff8000","#ffff00","#80ff00","#00ff00","#00ff80","#00ffff","#0080ff","#0000ff","#8000ff","#ff00ff","#ff0080","#ffffff","#c0c0c0","#808080","#000000"]),[D,ne]=N.useState(()=>{const h=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec("#ffffff");return h?{r:parseInt(h[1],16),g:parseInt(h[2],16),b:parseInt(h[3],16)}:{r:255,g:255,b:255}}),Z=N.useRef(null),we=N.useRef(null),C=N.useRef(null),_=N.useCallback(h=>{const v=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(h);return v?{r:parseInt(v[1],16),g:parseInt(v[2],16),b:parseInt(v[3],16)}:{r:0,g:0,b:0}},[]),A=N.useCallback((h,v,W)=>{const Y=H=>{const Q=Math.max(0,Math.min(255,Math.round(H))).toString(16);return Q.length===1?"0"+Q:Q};return`#${Y(h)}${Y(v)}${Y(W)}`},[]),J=N.useCallback(h=>/^#[0-9A-F]{6}$/i.test(h),[]),ke=N.useCallback(h=>{J(h)&&$(v=>{const W=v.filter(Y=>Y!==h);return[h,...W].slice(0,16)})},[J]),Ye=[{name:"Original",values:{brightness:100,contrast:100,saturation:100,hue:0,blur:0}},{name:"Vintage",values:{brightness:110,contrast:120,saturation:80,hue:10,blur:.5}},{name:"Sepia",values:{brightness:110,contrast:110,saturation:60,hue:30,blur:0}},{name:"Black & White",values:{brightness:100,contrast:120,saturation:0,hue:0,blur:0}},{name:"Cool",values:{brightness:105,contrast:110,saturation:120,hue:-10,blur:0}},{name:"Warm",values:{brightness:110,contrast:105,saturation:110,hue:15,blur:0}},{name:"Dramatic",values:{brightness:95,contrast:140,saturation:130,hue:0,blur:0}},{name:"Soft",values:{brightness:115,contrast:90,saturation:95,hue:5,blur:1}}];N.useEffect(()=>{if(e){const h={...xl};a(h),i([h]),u(0)}},[e]);const He=N.useCallback(()=>{if(!Z.current||!we.current||!e)return;const h=Z.current,v=h.getContext("2d");if(!v)return;const W=we.current;h.width=W.naturalWidth,h.height=W.naturalHeight,v.clearRect(0,0,h.width,h.height),v.save(),v.translate(h.width/2,h.height/2),v.rotate(l.rotation*Math.PI/180),v.scale(l.flipX?-1:1,l.flipY?-1:1);const Y=[`brightness(${l.brightness}%)`,`contrast(${l.contrast}%)`,`saturate(${l.saturation}%)`,`hue-rotate(${l.hue}deg)`,`blur(${l.blur}px)`];if(v.filter=Y.join(" "),v.drawImage(W,-h.width/2,-h.height/2,h.width,h.height),v.restore(),l.textOverlays.forEach(H=>{v.save(),v.font=`${H.bold?"bold":""} ${H.italic?"italic":""} ${H.fontSize}px ${H.fontFamily}`,v.fillStyle=H.color,v.strokeStyle="rgba(0,0,0,0.8)",v.lineWidth=2;const Q=H.x/100*h.width,re=H.y/100*h.height;v.strokeText(H.text,Q,re),v.fillText(H.text,Q,re),v.restore()}),l.drawings.forEach(H=>{if(H.points.length!==0){if(v.save(),v.globalAlpha=H.opacity||1,v.strokeStyle=H.color,v.lineWidth=H.strokeWidth,v.lineCap="round",v.lineJoin="round",H.type==="brush")v.beginPath(),v.moveTo(H.points[0].x,H.points[0].y),H.points.forEach(Q=>{v.lineTo(Q.x,Q.y)}),v.stroke();else if(H.type==="rectangle"){const Q=H.points[0],re=H.points[H.points.length-1],Be=re.x-Q.x,bn=re.y-Q.y;H.fill?(v.fillStyle=H.color,v.fillRect(Q.x,Q.y,Be,bn)):v.strokeRect(Q.x,Q.y,Be,bn)}else if(H.type==="circle"){const Q=H.points[0],re=H.points[H.points.length-1],Be=Math.sqrt(Math.pow(re.x-Q.x,2)+Math.pow(re.y-Q.y,2));v.beginPath(),v.arc(Q.x,Q.y,Be,0,2*Math.PI),H.fill?(v.fillStyle=H.color,v.fill()):v.stroke()}v.restore()}}),E&&E.points.length>0){if(v.save(),v.globalAlpha=E.opacity||1,v.strokeStyle=E.color,v.lineWidth=E.strokeWidth,v.lineCap="round",v.lineJoin="round",E.type==="brush")v.beginPath(),v.moveTo(E.points[0].x,E.points[0].y),E.points.forEach(H=>{v.lineTo(H.x,H.y)}),v.stroke();else if(E.type==="rectangle"){const H=E.points[0],Q=E.points[E.points.length-1],re=Q.x-H.x,Be=Q.y-H.y;v.strokeRect(H.x,H.y,re,Be)}else if(E.type==="circle"){const H=E.points[0],Q=E.points[E.points.length-1],re=Math.sqrt(Math.pow(Q.x-H.x,2)+Math.pow(Q.y-H.y,2));v.beginPath(),v.arc(H.x,H.y,re,0,2*Math.PI),v.stroke()}v.restore()}},[l,e]);N.useEffect(()=>{if(!e||!we.current)return;const h=we.current,v=()=>He();return h.addEventListener("load",v),h.src=e.url,()=>{h.removeEventListener("load",v),h.onload=null,h.onerror=null}},[e,He]),N.useEffect(()=>{He()},[He]),N.useEffect(()=>{if(E){const h=setTimeout(()=>{He()},8);return()=>clearTimeout(h)}},[E,He]),N.useEffect(()=>{const h=_(L);(h.r!==D.r||h.g!==D.g||h.b!==D.b)&&ne(h)},[L,D.r,D.g,D.b,_]);const be=h=>{const v={...l,...h};a(v);const W=o.slice(0,c+1);W.push(v),i(W),u(W.length-1)},vs=()=>{if(c>0){const h=c-1;u(h),a(o[h])}},ws=()=>{if(c<o.length-1){const h=c+1;u(h),a(o[h])}},js=()=>{const h={...xl};a(h),i([h]),u(0)},nr=h=>{be(h.values)},Ns=()=>{const h={id:`text-${Date.now()}`,text:"Sample Text",x:50,y:50,fontSize:24,fontFamily:"Arial",color:"#ffffff",bold:!1,italic:!1};be({textOverlays:[...l.textOverlays,h]}),b(h.id)},nn=(h,v)=>{const W=l.textOverlays.map(Y=>Y.id===h?{...Y,...v}:Y);be({textOverlays:W})},ks=h=>{const v=l.textOverlays.filter(W=>W.id!==h);be({textOverlays:v}),p===h&&b(null)},bs=()=>{if(!Z.current||!we.current)return;const h=Z.current,v=h.getContext("2d");if(!v)return;const{x:W,y:Y,width:H,height:Q}=d;if(H===0||Q===0)return;const re=document.createElement("canvas"),Be=re.getContext("2d");Be&&(re.width=H,re.height=Q,Be.drawImage(h,W,Y,H,Q,0,0,H,Q),h.width=H,h.height=Q,v.clearRect(0,0,H,Q),v.drawImage(re,0,0),f(!1),m({x:0,y:0,width:0,height:0}))},kn=h=>{if(!Z.current||!we.current)return;const v=Z.current,W=v.getContext("2d");if(!W)return;const Y=we.current,H=Math.round(Y.naturalWidth*h),Q=Math.round(Y.naturalHeight*h),re=document.createElement("canvas"),Be=re.getContext("2d");Be&&(re.width=v.width,re.height=v.height,Be.drawImage(v,0,0),v.width=H,v.height=Q,W.clearRect(0,0,H,Q),W.drawImage(Y,0,0,H,Q),He())},Cs=()=>{if(!Z.current)return;const v=Z.current.toDataURL("image/png"),W={brightness:l.brightness,contrast:l.contrast,saturation:l.saturation,hue:l.hue,blur:l.blur,rotation:l.rotation,flipX:l.flipX,flipY:l.flipY};r(v,"filters_and_transform",W),n()},Kr=h=>{const v=Z.current;if(!v)return{x:0,y:0};const W=v.getBoundingClientRect(),Y=h.clientX-W.left,H=h.clientY-W.top,Q=v.width/W.width,re=v.height/W.height;return{x:Y*Q,y:H*re}},Ss=h=>{var H;const v=(H=C.current)==null?void 0:H.getBoundingClientRect();if(!v)return;const W=h.clientX-v.left,Y=h.clientY-v.top;if(U)j(!0),k({x:W,y:Y}),m({x:W,y:Y,width:0,height:0});else if(M&&x==="draw"){const Q=Kr(h),re={id:`drawing-${Date.now()}`,type:M,points:[Q],color:L,strokeWidth:ve,opacity:ae,fill:!1};B(re),j(!0)}else j(!0),k({x:h.clientX,y:h.clientY})},Es=h=>{var H;const v=(H=C.current)==null?void 0:H.getBoundingClientRect();if(!v)return;const W=h.clientX-v.left,Y=h.clientY-v.top;if(g)if(U){const Q=W-w.x,re=Y-w.y;m({x:Math.min(w.x,W),y:Math.min(w.y,Y),width:Math.abs(Q),height:Math.abs(re)})}else if(E&&M){const Q=Kr(h);if(M==="brush"){const re={...E,points:[...E.points,Q]};B(re)}else{const re={...E,points:[E.points[0],Q]};B(re)}}else{const Q=h.clientX-w.x,re=h.clientY-w.y;be({panX:l.panX+Q,panY:l.panY+re}),k({x:h.clientX,y:h.clientY})}},Yr=()=>{E&&M&&(be({drawings:[...l.drawings,E]}),B(null)),j(!1)};return!t||!e?null:s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-gray-900 rounded-lg w-full h-full max-w-7xl max-h-[90vh] flex flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700",children:[s.jsx("h2",{className:"text-xl font-semibold text-white",children:"Image Editor"}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("button",{onClick:vs,disabled:c===0,className:"p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed",title:"Undo",children:s.jsx(Bp,{size:20})}),s.jsx("button",{onClick:ws,disabled:c===o.length-1,className:"p-2 text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed",title:"Redo",children:s.jsx(Fp,{size:20})}),s.jsx("button",{onClick:js,className:"px-3 py-1 text-sm bg-gray-700 hover:bg-gray-600 text-white rounded",children:"Reset"}),s.jsxs("button",{onClick:Cs,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded flex items-center gap-2",children:[s.jsx(Xr,{size:16}),"Save"]}),s.jsx("button",{onClick:n,className:"p-2 text-gray-400 hover:text-white",children:s.jsx(yt,{size:20})})]})]}),s.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[s.jsxs("div",{className:"w-80 bg-gray-800 border-r border-gray-700 flex flex-col",children:[s.jsx("div",{className:"flex flex-wrap border-b border-gray-700",children:[{id:"adjust",label:"Basic",icon:_d},{id:"advanced",label:"Advanced",icon:Hp},{id:"filters",label:"Filters",icon:Md},{id:"transform",label:"Transform",icon:Op},{id:"crop",label:"Crop",icon:Sp},{id:"text",label:"Text",icon:pc},{id:"draw",label:"Draw",icon:uc},{id:"resize",label:"Resize",icon:Ap}].map(({id:h,label:v,icon:W})=>s.jsxs("button",{onClick:()=>y(h),className:`flex-1 min-w-0 p-2 flex flex-col items-center gap-1 text-xs font-medium ${x===h?"bg-blue-600 text-white":"text-gray-400 hover:text-white hover:bg-gray-700"}`,children:[s.jsx(W,{size:14}),s.jsx("span",{className:"truncate",children:v})]},h))}),s.jsxs("div",{className:"flex-1 p-4 overflow-y-auto",children:[x==="adjust"&&s.jsx("div",{className:"space-y-4",children:[{key:"brightness",label:"Brightness",min:0,max:200,step:1},{key:"contrast",label:"Contrast",min:0,max:200,step:1},{key:"saturation",label:"Saturation",min:0,max:200,step:1},{key:"hue",label:"Hue",min:-180,max:180,step:1},{key:"blur",label:"Blur",min:0,max:10,step:.1}].map(({key:h,label:v,min:W,max:Y,step:H})=>s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[v,": ",l[h],h==="brightness"||h==="contrast"||h==="saturation"?"%":"",h==="hue"?"°":"",h==="blur"?"px":""]}),s.jsx("input",{type:"range",min:W,max:Y,step:H,value:l[h],onChange:Q=>be({[h]:parseFloat(Q.target.value)}),className:"w-full slider"})]},h))}),x==="advanced"&&s.jsx("div",{className:"space-y-4",children:[{key:"shadows",label:"Shadows",min:-100,max:100,step:1},{key:"highlights",label:"Highlights",min:-100,max:100,step:1},{key:"temperature",label:"Temperature",min:-100,max:100,step:1},{key:"tint",label:"Tint",min:-100,max:100,step:1},{key:"exposure",label:"Exposure",min:-100,max:100,step:1}].map(({key:h,label:v,min:W,max:Y,step:H})=>s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:[v,": ",l[h]]}),s.jsx("input",{type:"range",min:W,max:Y,step:H,value:l[h],onChange:Q=>be({[h]:parseFloat(Q.target.value)}),className:"w-full slider"})]},h))}),x==="filters"&&s.jsxs("div",{className:"space-y-4",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-300 mb-3",children:"Preset Filters"}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:Ye.map(h=>s.jsx("button",{onClick:()=>nr(h),className:"p-3 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm transition-colors",children:h.name},h.name))})]}),x==="transform"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Rotation: ",l.rotation,"°"]}),s.jsx("input",{type:"range",min:-180,max:180,step:1,value:l.rotation,onChange:h=>be({rotation:parseInt(h.target.value)}),className:"w-full slider"})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("button",{onClick:()=>be({flipX:!l.flipX}),className:`w-full p-2 rounded text-sm font-medium ${l.flipX?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"Flip Horizontal"}),s.jsx("button",{onClick:()=>be({flipY:!l.flipY}),className:`w-full p-2 rounded text-sm font-medium ${l.flipY?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:"Flip Vertical"})]})]}),x==="crop"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"space-y-2",children:[s.jsx("button",{onClick:()=>f(!U),className:`w-full p-3 rounded text-sm font-medium ${U?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:U?"Exit Crop Mode":"Enter Crop Mode"}),U&&s.jsx("button",{onClick:bs,className:"w-full p-2 bg-green-600 hover:bg-green-700 text-white rounded text-sm",children:"Apply Crop"})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Zoom: ",Math.round(l.zoom*100),"%"]}),s.jsx("input",{type:"range",min:.1,max:3,step:.1,value:l.zoom,onChange:h=>be({zoom:parseFloat(h.target.value)}),className:"w-full slider"})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsxs("button",{onClick:()=>be({zoom:Math.min(3,l.zoom+.1)}),className:"flex-1 p-2 bg-gray-700 hover:bg-gray-600 text-white rounded flex items-center justify-center gap-2",children:[s.jsx(Xp,{size:16}),"Zoom In"]}),s.jsxs("button",{onClick:()=>be({zoom:Math.max(.1,l.zoom-.1)}),className:"flex-1 p-2 bg-gray-700 hover:bg-gray-600 text-white rounded flex items-center justify-center gap-2",children:[s.jsx(Qp,{size:16}),"Zoom Out"]})]})]}),x==="text"&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("button",{onClick:Ns,className:"w-full p-3 bg-blue-600 hover:bg-blue-700 text-white rounded flex items-center justify-center gap-2",children:[s.jsx(pc,{size:16}),"Add Text"]}),l.textOverlays.length>0&&s.jsxs("div",{className:"space-y-3",children:[s.jsx("h4",{className:"text-sm font-medium text-gray-300",children:"Text Overlays"}),l.textOverlays.map(h=>s.jsxs("div",{className:"p-3 bg-gray-700 rounded space-y-2",children:[s.jsx("input",{type:"text",value:h.text,onChange:v=>nn(h.id,{text:v.target.value}),className:"w-full p-2 bg-gray-600 text-white rounded text-sm",placeholder:"Enter text..."}),s.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"Size"}),s.jsx("input",{type:"range",min:12,max:72,value:h.fontSize,onChange:v=>nn(h.id,{fontSize:parseInt(v.target.value)}),className:"w-full slider"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"Color"}),s.jsx("input",{type:"color",value:h.color,onChange:v=>nn(h.id,{color:v.target.value}),className:"w-full h-8 bg-gray-600 rounded"})]})]}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx("button",{onClick:()=>nn(h.id,{bold:!h.bold}),className:`flex-1 p-1 rounded text-xs ${h.bold?"bg-blue-600 text-white":"bg-gray-600 text-gray-300"}`,children:"Bold"}),s.jsx("button",{onClick:()=>nn(h.id,{italic:!h.italic}),className:`flex-1 p-1 rounded text-xs ${h.italic?"bg-blue-600 text-white":"bg-gray-600 text-gray-300"}`,children:"Italic"}),s.jsx("button",{onClick:()=>ks(h.id),className:"flex-1 p-1 bg-red-600 hover:bg-red-700 text-white rounded text-xs",children:"Delete"})]})]},h.id))]})]}),x==="draw"&&s.jsxs("div",{className:"space-y-4",children:[s.jsx("div",{className:"grid grid-cols-2 gap-2",children:[{mode:"brush",label:"Brush",icon:uc},{mode:"rectangle",label:"Rectangle",icon:Td},{mode:"circle",label:"Circle",icon:bp}].map(({mode:h,label:v,icon:W})=>s.jsxs("button",{onClick:()=>F(M===h?null:h),className:`p-3 rounded flex flex-col items-center gap-1 text-xs ${M===h?"bg-blue-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"}`,children:[s.jsx(W,{size:16}),v]},h))}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300",children:"Color"}),s.jsx("button",{onClick:()=>{ct(!ge),ge||ne(_(L))},className:"text-xs text-blue-400 hover:text-blue-300",children:ge?"Simple":"Advanced"})]}),ge?s.jsxs("div",{className:"space-y-3 p-3 bg-gray-700 rounded",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs text-gray-400 mb-2",children:"Quick Colors"}),s.jsx("div",{className:"grid grid-cols-8 gap-1",children:O.slice(0,16).map((h,v)=>s.jsx("button",{onClick:()=>{q(h),ke(h),ne(_(h))},className:`w-6 h-6 rounded border-2 ${L===h?"border-white":"border-gray-500"}`,style:{backgroundColor:h}},v))})]}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("label",{className:"block text-xs text-gray-400",children:"RGB Values"}),s.jsxs("div",{className:"space-y-1",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-red-400 w-4",children:"R"}),s.jsx("input",{type:"range",min:0,max:255,value:D.r,onChange:h=>{const v=parseInt(h.target.value),W={...D,r:v};ne(W);const Y=A(W.r,W.g,W.b);Y!==L&&q(Y)},className:"flex-1 slider"}),s.jsx("span",{className:"text-xs text-gray-400 w-8",children:D.r})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-green-400 w-4",children:"G"}),s.jsx("input",{type:"range",min:0,max:255,value:D.g,onChange:h=>{const v=parseInt(h.target.value),W={...D,g:v};ne(W);const Y=A(W.r,W.g,W.b);Y!==L&&q(Y)},className:"flex-1 slider"}),s.jsx("span",{className:"text-xs text-gray-400 w-8",children:D.g})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-blue-400 w-4",children:"B"}),s.jsx("input",{type:"range",min:0,max:255,value:D.b,onChange:h=>{const v=parseInt(h.target.value),W={...D,b:v};ne(W);const Y=A(W.r,W.g,W.b);Y!==L&&q(Y)},className:"flex-1 slider"}),s.jsx("span",{className:"text-xs text-gray-400 w-8",children:D.b})]})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-xs text-gray-400 mb-1",children:"Hex Color"}),s.jsx("input",{type:"text",value:L,onChange:h=>{let v=h.target.value.trim();v&&!v.startsWith("#")&&(v="#"+v),(v==="#"||/^#[0-9A-F]{0,6}$/i.test(v))&&(v.length===7?(q(v.toLowerCase()),ke(v.toLowerCase())):v.length>=1&&q(v.toLowerCase()))},onBlur:h=>{const v=h.target.value.trim();if(v.length===7&&/^#[0-9A-F]{6}$/i.test(v))q(v.toLowerCase()),ke(v.toLowerCase());else if(v.length<7&&v.length>1){const W=v+"0".repeat(7-v.length);/^#[0-9A-F]{6}$/i.test(W)&&(q(W.toLowerCase()),ke(W.toLowerCase()))}},className:"w-full p-1 bg-gray-600 text-white rounded text-xs font-mono",placeholder:"#ffffff",maxLength:7})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("span",{className:"text-xs text-gray-400",children:"Preview:"}),s.jsx("div",{className:"w-8 h-8 rounded border border-gray-500",style:{backgroundColor:L}})]})]}):s.jsx("input",{type:"color",value:L,onChange:h=>{q(h.target.value),ke(h.target.value),ne(_(h.target.value))},className:"w-full h-10 bg-gray-600 rounded"})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Stroke Width: ",ve,"px"]}),s.jsx("input",{type:"range",min:1,max:20,value:ve,onChange:h=>G(parseInt(h.target.value)),className:"w-full slider"})]}),s.jsxs("div",{children:[s.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:["Opacity: ",Math.round(ae*100),"%"]}),s.jsx("input",{type:"range",min:.1,max:1,step:.1,value:ae,onChange:h=>ie(parseFloat(h.target.value)),className:"w-full slider"})]})]}),l.drawings.length>0&&s.jsx("button",{onClick:()=>be({drawings:[]}),className:"w-full p-2 bg-red-600 hover:bg-red-700 text-white rounded text-sm",children:"Clear All Drawings"})]}),x==="resize"&&s.jsx("div",{className:"space-y-4",children:s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Resize Options"}),s.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[s.jsx("button",{onClick:()=>kn(.5),className:"p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm",children:"50%"}),s.jsx("button",{onClick:()=>kn(.75),className:"p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm",children:"75%"}),s.jsx("button",{onClick:()=>kn(1.25),className:"p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm",children:"125%"}),s.jsx("button",{onClick:()=>kn(2),className:"p-2 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm",children:"200%"})]})]}),s.jsxs("div",{className:"p-3 bg-gray-700 rounded",children:[s.jsx("p",{className:"text-xs text-gray-400 mb-2",children:"Current Size"}),s.jsxs("p",{className:"text-sm text-white",children:[((Zr=Z.current)==null?void 0:Zr.width)||0," × ",((qr=Z.current)==null?void 0:qr.height)||0," pixels"]})]})]})})]})]}),s.jsx("div",{className:"flex-1 bg-gray-900 flex items-center justify-center p-4",children:s.jsxs("div",{ref:C,className:"relative max-w-full max-h-full overflow-hidden rounded-lg bg-gray-800",onMouseDown:Ss,onMouseMove:Es,onMouseUp:Yr,onMouseLeave:Yr,children:[s.jsx("canvas",{ref:Z,className:"max-w-full max-h-full object-contain cursor-move",style:{transform:`scale(${l.zoom}) translate(${l.panX}px, ${l.panY}px)`}}),U&&s.jsx("div",{className:"absolute border-2 border-white border-dashed bg-black bg-opacity-30",style:{left:d.x,top:d.y,width:d.width,height:d.height,pointerEvents:"none"}}),l.textOverlays.map(h=>s.jsx("div",{className:"absolute pointer-events-none",style:{left:`${h.x}%`,top:`${h.y}%`,fontSize:`${h.fontSize}px`,fontFamily:h.fontFamily,color:h.color,fontWeight:h.bold?"bold":"normal",fontStyle:h.italic?"italic":"normal",textShadow:"1px 1px 2px rgba(0,0,0,0.8)",transform:`scale(${l.zoom})`},children:h.text},h.id)),s.jsx("img",{ref:we,src:e.url,alt:"Original",className:"hidden",crossOrigin:"anonymous"})]})})]})]})})},ch=({images:e,isOpen:t,onClose:n,onDownload:r,onDelete:l,onEdit:a})=>{const[o,i]=N.useState([]),[c,u]=N.useState([]),[x,y]=N.useState(1),[g,j]=N.useState(0),[w,k]=N.useState(0);N.useEffect(()=>{if(t&&e.length>0){const p=e.filter(b=>!b.isLoading);u(p),p.length>=2?i([p[0],p[1]]):p.length===1?i([p[0]]):i([]),y(1),j(0),k(0)}},[t,e]),N.useEffect(()=>{const p=b=>{b.key==="Escape"&&n()};return t&&(document.addEventListener("keydown",p),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",p),document.body.style.overflow="unset"}},[t,n]);const U=p=>{o.length<4&&!o.find(b=>b.id===p.id)&&i(b=>[...b,p])},f=p=>{i(b=>b.filter(M=>M.id!==p))},d=()=>{y(1),j(0),k(0)},m=p=>new Date(p).toLocaleString();return t?s.jsxs("div",{className:"fixed inset-0 bg-black bg-opacity-95 flex flex-col z-50",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900",children:[s.jsxs("div",{className:"flex items-center gap-4",children:[s.jsx("h2",{className:"text-xl font-semibold text-white",children:"Image Comparison"}),s.jsxs("span",{className:"text-sm text-gray-400",children:[o.length," of 4 images selected"]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsxs("div",{className:"flex items-center gap-2 bg-gray-800 rounded-lg p-2",children:[s.jsx("button",{onClick:()=>y(p=>Math.max(.25,p-.25)),className:"p-1 text-gray-400 hover:text-white transition-colors",title:"Zoom Out",children:s.jsx(dc,{size:16})}),s.jsxs("span",{className:"text-sm text-gray-300 min-w-[60px] text-center",children:[Math.round(x*100),"%"]}),s.jsx("button",{onClick:()=>y(p=>Math.min(3,p+.25)),className:"p-1 text-gray-400 hover:text-white transition-colors",title:"Zoom In",children:s.jsx(mn,{size:16})}),s.jsx("button",{onClick:d,className:"p-1 text-gray-400 hover:text-white transition-colors ml-2",title:"Reset View",children:s.jsx(Rp,{size:16})})]}),s.jsx("button",{onClick:n,className:"p-2 text-gray-400 hover:text-white transition-colors",children:s.jsx(yt,{size:20})})]})]}),s.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[s.jsxs("div",{className:"w-80 bg-gray-800 border-r border-gray-700 flex flex-col",children:[s.jsxs("div",{className:"p-4 border-b border-gray-700",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Available Images"}),s.jsx("p",{className:"text-sm text-gray-400",children:"Click to add images to comparison (max 4)"})]}),s.jsx("div",{className:"flex-1 overflow-y-auto p-4",children:s.jsx("div",{className:"grid grid-cols-2 gap-3",children:c.map(p=>{const b=o.find(M=>M.id===p.id);return s.jsxs("div",{className:`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${b?"border-accent-primary shadow-glow-sm":"border-transparent hover:border-gray-600"}`,onClick:()=>{b?f(p.id):U(p)},children:[s.jsx("img",{src:p.url,alt:p.prompt,className:"w-full aspect-square object-cover"}),s.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center",children:b?s.jsx("div",{className:"bg-accent-primary text-white rounded-full p-2",children:s.jsx(dc,{size:16})}):s.jsx("div",{className:"bg-white text-gray-900 rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity",children:s.jsx(mn,{size:16})})}),s.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2",children:s.jsx("p",{className:"text-xs text-white truncate",children:p.prompt})})]},p.id)})})})]}),s.jsx("div",{className:"flex-1 flex flex-col",children:o.length===0?s.jsx("div",{className:"flex-1 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(mn,{className:"w-10 h-10 text-gray-400"})}),s.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Images Selected"}),s.jsx("p",{className:"text-gray-400",children:"Select images from the sidebar to start comparing"})]})}):s.jsx(s.Fragment,{children:s.jsx("div",{className:"flex-1 p-4",children:s.jsx("div",{className:`grid gap-4 h-full ${o.length===1?"grid-cols-1":o.length===2?"grid-cols-2":o.length===3?"grid-cols-3":"grid-cols-2 grid-rows-2"}`,children:o.map(p=>s.jsxs("div",{className:"relative bg-gray-800 rounded-lg overflow-hidden group",children:[s.jsxs("div",{className:"relative h-full overflow-hidden",children:[s.jsx("img",{src:p.url,alt:p.prompt,className:"w-full h-full object-contain transition-transform duration-200",style:{transform:`scale(${x}) translate(${g}px, ${w}px)`},draggable:!1}),s.jsxs("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity flex gap-2",children:[a&&s.jsx("button",{onClick:()=>a(p),className:"p-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors",title:"Edit",children:s.jsx(Qr,{size:16})}),s.jsx("button",{onClick:()=>r(p),className:"p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",title:"Download",children:s.jsx(Xr,{size:16})}),s.jsx("button",{onClick:()=>l(p),className:"p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",title:"Delete",children:s.jsx(Nn,{size:16})}),s.jsx("button",{onClick:()=>f(p.id),className:"p-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",title:"Remove from comparison",children:s.jsx(yt,{size:16})})]})]}),s.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/80 to-transparent p-3",children:[s.jsx("p",{className:"text-sm text-white font-medium mb-1 line-clamp-2",children:p.prompt}),s.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-300",children:[s.jsx("span",{className:"capitalize",children:p.service}),s.jsx("span",{children:m(p.timestamp)})]})]})]},p.id))})})})})]})]}):null},Sn=["#3b82f6","#ef4444","#10b981","#f59e0b","#8b5cf6","#ec4899","#06b6d4","#84cc16","#f97316","#6366f1"],uh=({isOpen:e,onClose:t,images:n,collections:r,onCreateCollection:l,onUpdateCollection:a,onDeleteCollection:o,onAddImageToCollection:i,onRemoveImageFromCollection:c,selectedImages:u=new Set,onSelectCollection:x})=>{const[y,g]=N.useState(!1),[j,w]=N.useState(null),[k,U]=N.useState(""),[f,d]=N.useState(""),[m,p]=N.useState(Sn[0]),[b,M]=N.useState(null);N.useEffect(()=>{const G=ae=>{ae.key==="Escape"&&(y||j?B():t())};return e&&(document.addEventListener("keydown",G),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",G),document.body.style.overflow="unset"}},[e,y,j,t]);const F=()=>{k.trim()&&(l({name:k.trim(),description:f.trim()||void 0,imageIds:Array.from(u),color:m}),B())},E=()=>{!j||!k.trim()||(a(j.id,{name:k.trim(),description:f.trim()||void 0,color:m,updatedAt:Date.now()}),B())},B=()=>{g(!1),w(null),U(""),d(""),p(Sn[0])},L=G=>{w(G),U(G.name),d(G.description||""),p(G.color||Sn[0])},q=G=>{u.forEach(ae=>{i(G,ae)})},ve=G=>n.filter(ae=>G.imageIds.includes(ae.id));return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50",children:s.jsxs("div",{className:"bg-gray-900 rounded-lg w-full h-full max-w-6xl max-h-[90vh] flex flex-col",children:[s.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-700",children:[s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsx(br,{className:"w-6 h-6 text-accent-primary"}),s.jsx("h2",{className:"text-2xl font-semibold text-white",children:"Collections"}),s.jsxs("span",{className:"text-sm text-gray-400",children:["(",r.length," collection",r.length!==1?"s":"",")"]})]}),s.jsxs("div",{className:"flex items-center gap-3",children:[s.jsxs("button",{onClick:()=>g(!0),className:"flex items-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary text-white rounded-lg transition-colors",children:[s.jsx(Xo,{className:"w-4 h-4"}),"New Collection"]}),s.jsx("button",{onClick:t,className:"p-2 text-gray-400 hover:text-white transition-colors",children:s.jsx(yt,{className:"w-6 h-6"})})]})]}),s.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[s.jsxs("div",{className:"w-1/3 border-r border-gray-700 flex flex-col",children:[s.jsxs("div",{className:"p-4 border-b border-gray-700",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Your Collections"}),u.size>0&&s.jsxs("p",{className:"text-sm text-gray-400",children:[u.size," image",u.size!==1?"s":""," selected"]})]}),s.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-3",children:[r.map(G=>{const ae=ve(G),ie=b===G.id;return s.jsxs("div",{className:`p-4 rounded-lg border cursor-pointer transition-all ${ie?"border-accent-primary bg-accent-primary/10":"border-gray-700 hover:border-gray-600 bg-gray-800/50"}`,onClick:()=>{M(G.id),x==null||x(G)},children:[s.jsxs("div",{className:"flex items-start justify-between mb-2",children:[s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"w-4 h-4 rounded-full",style:{backgroundColor:G.color||Sn[0]}}),s.jsx("h4",{className:"font-medium text-white",children:G.name})]}),s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("button",{onClick:ge=>{ge.stopPropagation(),L(G)},className:"p-1 text-gray-400 hover:text-white transition-colors",title:"Edit collection",children:s.jsx(Qr,{className:"w-4 h-4"})}),!G.isDefault&&s.jsx("button",{onClick:ge=>{ge.stopPropagation(),o(G.id)},className:"p-1 text-gray-400 hover:text-red-400 transition-colors",title:"Delete collection",children:s.jsx(Nn,{className:"w-4 h-4"})})]})]}),G.description&&s.jsx("p",{className:"text-sm text-gray-400 mb-2",children:G.description}),s.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-400",children:[s.jsxs("span",{className:"flex items-center gap-1",children:[s.jsx(Wn,{className:"w-4 h-4"}),ae.length," image",ae.length!==1?"s":""]}),u.size>0&&s.jsxs("button",{onClick:ge=>{ge.stopPropagation(),q(G.id)},className:"px-2 py-1 text-xs bg-accent-primary hover:bg-accent-secondary text-white rounded transition-colors",children:["+ Add ",u.size]})]})]},G.id)}),r.length===0&&s.jsxs("div",{className:"text-center py-8",children:[s.jsx(br,{className:"w-12 h-12 text-gray-600 mx-auto mb-3"}),s.jsx("p",{className:"text-gray-400",children:"No collections yet"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Create your first collection to organize images"})]})]})]}),s.jsx("div",{className:"flex-1 flex flex-col",children:y||j?s.jsxs("div",{className:"p-6",children:[s.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:y?"Create New Collection":"Edit Collection"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Collection Name"}),s.jsx("input",{type:"text",value:k,onChange:G=>U(G.target.value),placeholder:"Enter collection name...",className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none",autoFocus:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description (Optional)"}),s.jsx("textarea",{value:f,onChange:G=>d(G.target.value),placeholder:"Enter collection description...",rows:3,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:border-accent-primary focus:outline-none resize-none"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Color"}),s.jsx("div",{className:"flex items-center gap-2",children:Sn.map(G=>s.jsx("button",{onClick:()=>p(G),className:`w-8 h-8 rounded-full border-2 transition-all ${m===G?"border-white scale-110":"border-gray-600 hover:border-gray-400"}`,style:{backgroundColor:G}},G))})]}),y&&u.size>0&&s.jsx("div",{className:"p-3 bg-gray-800 rounded-lg",children:s.jsxs("p",{className:"text-sm text-gray-300",children:[u.size," selected image",u.size!==1?"s":""," will be added to this collection"]})})]}),s.jsxs("div",{className:"flex items-center gap-3 mt-6",children:[s.jsxs("button",{onClick:y?F:E,disabled:!k.trim(),className:"flex items-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors",children:[s.jsx(ts,{className:"w-4 h-4"}),y?"Create Collection":"Save Changes"]}),s.jsx("button",{onClick:B,className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors",children:"Cancel"})]})]}):b?s.jsx("div",{className:"flex-1 p-6",children:(()=>{const G=r.find(ie=>ie.id===b);if(!G)return null;const ae=ve(G);return s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[s.jsx("div",{className:"w-6 h-6 rounded-full",style:{backgroundColor:G.color||Sn[0]}}),s.jsx("h3",{className:"text-xl font-semibold text-white",children:G.name}),s.jsxs("span",{className:"text-sm text-gray-400",children:["(",ae.length," image",ae.length!==1?"s":"",")"]})]}),G.description&&s.jsx("p",{className:"text-gray-300 mb-6",children:G.description}),ae.length>0?s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:ae.map(ie=>s.jsxs("div",{className:"relative group",children:[s.jsx("img",{src:ie.url,alt:ie.prompt,className:"w-full aspect-square object-cover rounded-lg"}),s.jsx("button",{onClick:()=>c(G.id,ie.id),className:"absolute top-2 right-2 p-1 bg-red-600 hover:bg-red-700 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity",title:"Remove from collection",children:s.jsx(yt,{className:"w-4 h-4"})}),s.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2 rounded-b-lg",children:s.jsx("p",{className:"text-xs text-white truncate",children:ie.prompt})})]},ie.id))}):s.jsxs("div",{className:"text-center py-12",children:[s.jsx(Wn,{className:"w-16 h-16 text-gray-600 mx-auto mb-4"}),s.jsx("p",{className:"text-gray-400 mb-2",children:"This collection is empty"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Select images from your gallery and add them to this collection"})]})]})})()}):s.jsx("div",{className:"flex-1 flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx(br,{className:"w-20 h-20 text-gray-600 mx-auto mb-4"}),s.jsx("h3",{className:"text-xl font-semibold text-white mb-2",children:"Select a Collection"}),s.jsx("p",{className:"text-gray-400",children:"Choose a collection from the left to view its contents"})]})})})]})]})}):null},dh=({className:e=""})=>{var we;const[t,n]=N.useState([]),[r,l]=N.useState(null),[a,o]=N.useState([]),[i,c]=N.useState(""),[u,x]=N.useState(!1),[y,g]=N.useState([]),[j,w]=N.useState("gpt-4"),[k,U]=N.useState("auto"),[f,d]=N.useState([]),[m,p]=N.useState([]),[b,M]=N.useState(!1),F=N.useRef(null),E=N.useRef(null);N.useEffect(()=>{B(),L()},[]),N.useEffect(()=>{ve()},[a,u]);const B=async()=>{if(!window.electronAPI){console.error("Electron API not available");return}try{const C=await window.electronAPI.gpt4freeChatIsConfigured();M(C.configured||!1);const _=await window.electronAPI.gpt4freeChatGetModels();_.success&&_.models?d(_.models):d(["gpt-4","gpt-3.5-turbo","claude-3-opus","claude-3-sonnet","gemini-pro","llama-2-70b","mixtral-8x7b","deepseek-coder"]);const A=await window.electronAPI.gpt4freeChatGetProviders();A.success&&A.providers?p(A.providers):p(["auto","bing","you","phind","liaobots","g4f"])}catch(C){console.error("Failed to initialize chatbot:",C),M(!1),d(["gpt-4","gpt-3.5-turbo","claude-3-opus"]),p(["auto","bing","you","phind"])}},L=()=>{const C=localStorage.getItem("gpt4free_conversations");if(C)try{const A=JSON.parse(C).map(J=>({...J,createdAt:new Date(J.createdAt),updatedAt:new Date(J.updatedAt)}));n(A)}catch(_){console.error("Error loading conversations:",_)}},q=C=>{localStorage.setItem("gpt4free_conversations",JSON.stringify(C)),n(C)},ve=()=>{var C;(C=F.current)==null||C.scrollIntoView({behavior:"smooth"})},G=()=>{const C={id:Date.now().toString(),title:"New Conversation",messages:[{role:"assistant",content:"Hello! I'm your GPT4Free AI assistant. How can I help you today?"}],model:j,provider:k,createdAt:new Date,updatedAt:new Date},_=[C,...t];q(_),l(C.id),o(C.messages)},ae=C=>{const _=t.find(A=>A.id===C);_&&(l(C),o(_.messages),w(_.model),U(_.provider))},ie=()=>{if(!r)return;const C=t.map(_=>{if(_.id===r){const A={..._,messages:[...a],model:j,provider:k,updatedAt:new Date};if(_.title==="New Conversation"&&a.length>1){const J=a.find(ke=>ke.role==="user");J&&(A.title=J.content.substring(0,50)+(J.content.length>50?"...":""))}return A}return _});q(C)},ge=async()=>{var A;if(!i.trim()&&y.length===0)return;const C={role:"user",content:i.trim()},_=[...a,C];o(_),c(""),x(!0);try{if(!b){setTimeout(()=>{const Ye={role:"assistant",content:`I understand your message: "${C.content}". However, the GPT4Free service is not currently configured. Please ensure Python and the g4f library are installed to get real AI responses.`};o(He=>[...He,Ye]),x(!1),ie()},1e3);return}if(!window.electronAPI)throw new Error("Electron API not available");const J=await window.electronAPI.gpt4freeChatGenerate({model:j,provider:k,messages:_,files:y});if(!J.success)throw new Error(J.error||"Failed to generate response");const ke={role:"assistant",content:((A=J.response)==null?void 0:A.response)||"No response received"};o(Ye=>[...Ye,ke]),g([])}catch(J){console.error("Error sending message:",J);const ke={role:"assistant",content:"Sorry, I encountered an error while processing your request. Please try again."};o(Ye=>[...Ye,ke])}finally{x(!1),ie()}},ct=C=>{const _=Array.from(C.target.files||[]);_.length!==0&&(_.forEach(A=>{if(A.size>10*1024*1024){alert(`File ${A.name} is too large. Maximum size is 10MB.`);return}const J=new FileReader;J.onload=ke=>{var He;const Ye={name:A.name,type:A.type,data:(He=ke.target)==null?void 0:He.result};g(be=>[...be,Ye])},J.readAsDataURL(A)}),E.current&&(E.current.value=""))},O=C=>{g(_=>_.filter((A,J)=>J!==C))},$=()=>{window.confirm("Are you sure you want to clear this conversation?")&&(o([{role:"assistant",content:"Hello! I'm your GPT4Free AI assistant. How can I help you today?"}]),ie())},D=()=>{if(!r)return;const C=t.find(Ye=>Ye.id===r);if(!C)return;const _={title:C.title,model:C.model,provider:C.provider,createdAt:C.createdAt,messages:C.messages},A=new Blob([JSON.stringify(_,null,2)],{type:"application/json"}),J=URL.createObjectURL(A),ke=document.createElement("a");ke.href=J,ke.download=`chat-${C.title.replace(/[^a-z0-9]/gi,"_").toLowerCase()}.json`,ke.click(),URL.revokeObjectURL(J)},ne=C=>{navigator.clipboard.writeText(C)},Z=C=>C.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"});return N.useEffect(()=>{t.length===0?G():!r&&t.length>0&&ae(t[0].id)},[t.length,r]),s.jsxs("div",{className:`flex h-full bg-gray-900 text-white ${e}`,children:[s.jsxs("div",{className:"w-80 bg-gray-800 border-r border-gray-700 flex flex-col",children:[s.jsxs("div",{className:"p-4 border-b border-gray-700",children:[s.jsxs("h1",{className:"text-xl font-bold flex items-center gap-2",children:[s.jsx(ta,{className:"w-6 h-6 text-blue-400"}),"GPT4Free Chat"]}),s.jsx("p",{className:"text-sm text-gray-400 mt-1",children:"Unlimited AI conversations"})]}),s.jsxs("div",{className:"p-4 border-b border-gray-700",children:[s.jsx("h3",{className:"text-sm font-medium text-blue-400 mb-3",children:"AI Model"}),s.jsx("select",{value:j,onChange:C=>w(C.target.value),className:"w-full p-2 bg-gray-700 border border-gray-600 rounded text-white text-sm mb-2",children:f.map(C=>s.jsx("option",{value:C,children:C.toUpperCase()},C))}),s.jsx("select",{value:k,onChange:C=>U(C.target.value),className:"w-full p-2 bg-gray-700 border border-gray-600 rounded text-white text-xs",children:m.map(C=>s.jsx("option",{value:C,children:C==="auto"?"Auto Select Provider":C.charAt(0).toUpperCase()+C.slice(1)},C))})]}),s.jsxs("div",{className:"flex-1 p-4 overflow-y-auto",children:[s.jsxs("div",{className:"flex items-center justify-between mb-3",children:[s.jsx("h3",{className:"text-sm font-medium text-blue-400",children:"Conversations"}),s.jsx("button",{onClick:G,className:"p-1 hover:bg-gray-700 rounded",title:"New Chat",children:s.jsx(mn,{className:"w-4 h-4"})})]}),s.jsxs("button",{onClick:G,className:"w-full p-3 bg-blue-600 hover:bg-blue-700 rounded-lg mb-3 flex items-center gap-2 text-sm font-medium transition-colors",children:[s.jsx(mn,{className:"w-4 h-4"}),"New Chat"]}),s.jsx("div",{className:"space-y-2",children:t.map(C=>s.jsxs("div",{onClick:()=>ae(C.id),className:`p-3 rounded-lg cursor-pointer transition-colors ${C.id===r?"bg-blue-600/20 border border-blue-500/30":"bg-gray-700/50 hover:bg-gray-700"}`,children:[s.jsx("div",{className:"font-medium text-sm truncate",children:C.title}),s.jsx("div",{className:"text-xs text-gray-400 mt-1",children:C.messages.length>1?C.messages[C.messages.length-1].content.substring(0,40)+"...":"No messages yet"})]},C.id))})]})]}),s.jsxs("div",{className:"flex-1 flex flex-col",children:[s.jsxs("div",{className:"p-4 border-b border-gray-700 flex items-center justify-between bg-gray-800",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"font-semibold",children:((we=t.find(C=>C.id===r))==null?void 0:we.title)||"New Conversation"}),s.jsxs("p",{className:"text-xs text-gray-400",children:[j," • ",k," • ",b?"Connected":"Demo Mode"]})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("button",{onClick:$,className:"p-2 hover:bg-gray-700 rounded",title:"Clear Chat",children:s.jsx(Nn,{className:"w-4 h-4"})}),s.jsx("button",{onClick:D,className:"p-2 hover:bg-gray-700 rounded",title:"Export Chat",children:s.jsx(Xr,{className:"w-4 h-4"})})]})]}),s.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:[a.map((C,_)=>s.jsxs("div",{className:`flex items-start gap-3 ${C.role==="user"?"flex-row-reverse":""}`,children:[s.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${C.role==="user"?"bg-blue-600":"bg-gray-600"}`,children:C.role==="user"?s.jsx(Vp,{className:"w-4 h-4"}):s.jsx(ta,{className:"w-4 h-4"})}),s.jsxs("div",{className:`max-w-[70%] ${C.role==="user"?"text-right":""}`,children:[s.jsx("div",{className:`p-3 rounded-lg ${C.role==="user"?"bg-blue-600 text-white":"bg-gray-700 text-gray-100"}`,children:s.jsx("div",{className:"whitespace-pre-wrap",children:C.content})}),s.jsxs("div",{className:`flex items-center gap-2 mt-1 text-xs text-gray-400 ${C.role==="user"?"justify-end":""}`,children:[s.jsx("span",{children:Z(new Date)}),C.role==="assistant"&&s.jsx("button",{onClick:()=>ne(C.content),className:"hover:text-gray-300",title:"Copy message",children:s.jsx(Cp,{className:"w-3 h-3"})})]})]})]},_)),u&&s.jsxs("div",{className:"flex items-start gap-3",children:[s.jsx("div",{className:"w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center",children:s.jsx(ta,{className:"w-4 h-4"})}),s.jsx("div",{className:"bg-gray-700 p-3 rounded-lg",children:s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce"}),s.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),s.jsx("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})})]}),s.jsx("div",{ref:F})]}),y.length>0&&s.jsx("div",{className:"p-4 border-t border-gray-700 bg-gray-800",children:s.jsx("div",{className:"flex flex-wrap gap-2",children:y.map((C,_)=>s.jsxs("div",{className:"flex items-center gap-2 bg-gray-700 p-2 rounded",children:[s.jsx("span",{className:"text-sm",children:C.name}),s.jsx("button",{onClick:()=>O(_),className:"text-red-400 hover:text-red-300",children:s.jsx(yt,{className:"w-4 h-4"})})]},_))})}),s.jsx("div",{className:"p-4 border-t border-gray-700 bg-gray-800",children:s.jsxs("div",{className:"flex items-end gap-2",children:[s.jsx("input",{type:"file",ref:E,onChange:ct,multiple:!0,className:"hidden"}),s.jsx("button",{onClick:()=>{var C;return(C=E.current)==null?void 0:C.click()},className:"p-2 hover:bg-gray-700 rounded",title:"Attach files",children:s.jsx(Lp,{className:"w-5 h-5"})}),s.jsx("div",{className:"flex-1",children:s.jsx("textarea",{value:i,onChange:C=>c(C.target.value),onKeyDown:C=>{C.key==="Enter"&&!C.shiftKey&&(C.preventDefault(),ge())},placeholder:"Type your message... (Shift+Enter for new line)",className:"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg resize-none focus:outline-none focus:border-blue-500",rows:1,style:{minHeight:"44px",maxHeight:"120px"}})}),s.jsx("button",{onClick:ge,disabled:!i.trim()&&y.length===0,className:"p-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded",children:s.jsx($p,{className:"w-5 h-5"})})]})})]})]})},fh=({currentView:e,onViewChange:t})=>s.jsx("div",{className:"bg-gray-800 border-b border-gray-700 px-4 py-3",children:s.jsxs("div",{className:"flex items-center gap-1",children:[s.jsxs("button",{onClick:()=>t("generator"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${e==="generator"?"bg-blue-600 text-white":"text-gray-300 hover:text-white hover:bg-gray-700"}`,children:[s.jsx(Wn,{className:"w-4 h-4"}),"Image Generator"]}),s.jsxs("button",{onClick:()=>t("chatbot"),className:`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${e==="chatbot"?"bg-blue-600 text-white":"text-gray-300 hover:text-white hover:bg-gray-700"}`,children:[s.jsx(Tp,{className:"w-4 h-4"}),"AI Chatbot"]})]})});class mh extends N.Component{constructor(n){super(n);ln(this,"handleReset",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})});this.state={hasError:!1}}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("Error caught by boundary:",n,r),this.setState({error:n,errorInfo:r})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:s.jsx("div",{className:"min-h-screen bg-app flex items-center justify-center p-8",children:s.jsxs("div",{className:"max-w-md w-full bg-card rounded-lg border border-matte p-6 text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(xp,{className:"w-8 h-8 text-red-500"})}),s.jsx("h2",{className:"text-xl font-semibold text-white mb-2",children:"Something went wrong"}),s.jsx("p",{className:"text-gray-400 mb-6",children:"An unexpected error occurred. Please try refreshing the page or restart the application."}),this.state.error&&s.jsxs("details",{className:"mb-6 text-left",children:[s.jsx("summary",{className:"text-sm text-gray-500 cursor-pointer hover:text-gray-400 mb-2",children:"Error Details"}),s.jsxs("div",{className:"bg-gray-800 rounded p-3 text-xs text-gray-300 font-mono overflow-auto max-h-32",children:[s.jsxs("div",{className:"mb-2",children:[s.jsx("strong",{children:"Error:"})," ",this.state.error.message]}),this.state.error.stack&&s.jsxs("div",{children:[s.jsx("strong",{children:"Stack:"}),s.jsx("pre",{className:"whitespace-pre-wrap mt-1",children:this.state.error.stack})]})]})]}),s.jsxs("div",{className:"flex gap-3 justify-center",children:[s.jsxs("button",{onClick:this.handleReset,className:"flex items-center gap-2 px-4 py-2 bg-accent-primary hover:bg-accent-secondary text-white rounded-lg transition-colors",children:[s.jsx(Ya,{className:"w-4 h-4"}),"Try Again"]}),s.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors",children:"Reload App"})]})]})}):this.props.children}}const ph=({toast:e,onClose:t})=>{const[n,r]=N.useState(!1);N.useEffect(()=>{r(!0);const o=setTimeout(()=>{r(!1),setTimeout(()=>t(e.id),300)},e.duration||4e3);return()=>clearTimeout(o)},[e.id,e.duration,t]);const l=()=>{switch(e.type){case"success":return s.jsx(wp,{className:"w-5 h-5 text-green-500"});case"error":return s.jsx(Wp,{className:"w-5 h-5 text-red-500"});case"warning":return s.jsx(Go,{className:"w-5 h-5 text-yellow-500"})}},a=()=>{switch(e.type){case"success":return"bg-green-900/30 border-green-500/40 shadow-glow-sm";case"error":return"bg-red-900/30 border-red-500/40 shadow-glow-sm";case"warning":return"bg-yellow-900/30 border-yellow-500/40 shadow-glow-sm"}};return s.jsx("div",{className:`
        fixed top-4 right-4 z-50 max-w-sm w-full
        transform transition-all duration-300 ease-in-out
        ${n?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
      `,children:s.jsx("div",{className:`p-4 rounded-2xl border ${a()} backdrop-blur-md glass-dark`,children:s.jsxs("div",{className:"flex items-start gap-3",children:[l(),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-semibold text-white",children:e.title}),e.message&&s.jsx("p",{className:"text-xs text-gray-300 mt-1 leading-relaxed",children:e.message})]}),s.jsx("button",{onClick:()=>t(e.id),className:"text-gray-400 hover:text-white transition-all duration-200 hover:scale-110 p-1 rounded-lg hover:bg-white/10",children:s.jsx(yt,{className:"w-4 h-4"})})]})})})},hh=({toasts:e,onClose:t})=>s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-3 pointer-events-none",children:e.map(n=>s.jsx("div",{className:"pointer-events-auto",children:s.jsx(ph,{toast:n,onClose:t})},n.id))});class gh{constructor(){ln(this,"baseUrls",["https://api.g4f.icu","https://g4f.ai","https://api.gpt4free.io"]);ln(this,"currentUrlIndex",0);ln(this,"configured",!0)}async generateImages(t){const{prompt:n,model:r="flux",count:l=4,width:a=1024,height:o=1024,seed:i}=t;try{const c=[];for(let u=0;u<l;u++){const x=i!==void 0?i+u:Math.floor(Math.random()*1e6),y={model:r,prompt:n,response_format:"url",size:`${a}x${o}`,...i!==void 0&&{seed:x}};let g=null,j=null;for(let k=0;k<this.baseUrls.length;k++){const U=this.baseUrls[(this.currentUrlIndex+k)%this.baseUrls.length];try{if(g=await fetch(`${U}/v1/images/generations`,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(y)}),g.ok){this.currentUrlIndex=(this.currentUrlIndex+k)%this.baseUrls.length;break}else j=new Error(`API error: ${g.status} ${g.statusText}`)}catch(f){j=f instanceof Error?f:new Error("Network error"),g=null}}if(!g||!g.ok){const k=await this.generateWithAlternativeEndpoint(n,r,a,o,x);if(k){c.push(k);continue}throw j||new Error("All GPT4Free endpoints failed")}const w=await g.json();if(w.data&&w.data.length>0){const k=w.data[0].url,U={id:`gpt4free-${Date.now()}-${u}-${Math.random().toString(36).substring(2,11)}`,url:k,prompt:n,timestamp:Date.now(),service:"gpt4free"};c.push(U)}}if(c.length===0)throw new Error("No images were generated");return c}catch(c){throw console.error("GPT4Free generation error:",c),c instanceof Error?c:new Error("Unknown error occurred during GPT4Free generation")}}async generateWithAlternativeEndpoint(t,n,r,l,a){var i,c;const o=["/generate/image","/api/generate","/v1/generate"];for(const u of this.baseUrls)for(const x of o)try{const y=new URLSearchParams({prompt:t,model:n,width:r.toString(),height:l.toString(),...a!==void 0&&{seed:a.toString()}}),g=await fetch(`${u}${x}?${y.toString()}`,{method:"GET",headers:{Accept:"application/json"}});if(g.ok){const j=await g.json();if(j.url||j.image_url||(c=(i=j.data)==null?void 0:i[0])!=null&&c.url){const w=j.url||j.image_url||j.data[0].url;return{id:`gpt4free-alt-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,url:w,prompt:t,timestamp:Date.now(),service:"gpt4free"}}}}catch{continue}return console.error("All alternative endpoints failed"),null}async testConnection(){for(const t of this.baseUrls)try{if((await fetch(`${t}/v1/models`,{method:"GET",headers:{Accept:"application/json"}})).ok)return!0}catch{continue}return console.error("GPT4Free connection test failed for all endpoints"),!1}isConfigured(){return this.configured}getAvailableModels(){return["flux","dalle-3","stable-diffusion-xl","playground-v2.5","ideogram"]}}const xh=new gh,yh=()=>{const[e,t]=N.useState([]),n=N.useCallback(i=>{const c=Math.random().toString(36).substring(2,11),u={...i,id:c};return t(x=>[...x,u]),setTimeout(()=>{t(x=>x.filter(y=>y.id!==c))},5e3),c},[]),r=N.useCallback(i=>{t(c=>c.filter(u=>u.id!==i))},[]),l=N.useCallback((i,c)=>n({type:"success",title:i,message:c}),[n]),a=N.useCallback((i,c)=>n({type:"error",title:i,message:c}),[n]),o=N.useCallback((i,c)=>n({type:"warning",title:i,message:c}),[n]);return{toasts:e,addToast:n,removeToast:r,success:l,error:a,warning:o}},yc={prompt:"",aspectRatio:"landscape",resolution:"1024x768",aiService:"pollinations",imagefxModel:"IMAGEN_4",pollinationsNoLogo:!0,pollinationsEnhance:!1,pollinationsSafe:!0,pollinationsPrivate:!1,pollinationsModel:"flux",imagefxCount:4,gpt4freeModel:"flux",gpt4freeCount:4};function vh(){const[e,t]=N.useState("generator"),[n,r]=N.useState([]),[l,a]=N.useState(yc),[o,i]=N.useState(!1),[c]=N.useState(()=>new Yp),[u,x]=N.useState(null),[y,g]=N.useState(!1),[j,w]=N.useState(!1),[k,U]=N.useState(!1),[f,d]=N.useState(null),[m,p]=N.useState(!1),[b,M]=N.useState(new Set),[F,E]=N.useState(!1),[B,L]=N.useState(""),[q,ve]=N.useState("all"),[G,ae]=N.useState("newest"),[ie,ge]=N.useState("all"),[ct,O]=N.useState(!1),[$,D]=N.useState([]),[ne,Z]=N.useState(!1),{toasts:we,removeToast:C,success:_,error:A,warning:J}=yh();N.useEffect(()=>{(async()=>{if(window.electronAPI)try{const S=await window.electronAPI.loadSettings();S.success&&S.settings&&a({...yc,...S.settings}),U(!0)}catch(S){console.error("Failed to load settings:",S),U(!0)}else U(!0)})(),(async()=>{if(window.electronAPI)try{const S=await window.electronAPI.loadGeneratedImages();S.success&&S.images&&r(S.images)}catch(S){console.error("Failed to load generated images:",S)}})()},[]),N.useEffect(()=>{(async()=>{if(window.electronAPI&&typeof window.electronAPI.loadCollections=="function")try{const I=await window.electronAPI.loadCollections();if(I.success&&I.collections&&I.collections.length>0){const S=I.collections,T=S.find(R=>R.isDefault);if(T){const K=n.map(te=>te.id).filter(te=>!T.imageIds.includes(te));if(K.length>0){const te=S.map(oe=>oe.isDefault?{...oe,imageIds:[...oe.imageIds,...K],updatedAt:Date.now()}:oe);D(te);try{typeof window.electronAPI.saveCollections=="function"&&await window.electronAPI.saveCollections(te)}catch(oe){console.error("Failed to save updated default collection:",oe)}}else D(S)}else D(S)}else{const R=[{id:"default",name:"All Images",description:"Default collection containing all images",imageIds:n.map(K=>K.id),createdAt:Date.now(),updatedAt:Date.now(),color:"#3b82f6",isDefault:!0}];D(R);try{typeof window.electronAPI.saveCollections=="function"&&await window.electronAPI.saveCollections(R)}catch(K){console.error("Failed to save default collection:",K)}}}catch(I){console.error("Failed to load collections:",I);const T={id:"default",name:"All Images",description:"Default collection containing all images",imageIds:n.map(R=>R.id),createdAt:Date.now(),updatedAt:Date.now(),color:"#3b82f6",isDefault:!0};D([T])}else{const S={id:"default",name:"All Images",description:"Default collection containing all images",imageIds:n.map(T=>T.id),createdAt:Date.now(),updatedAt:Date.now(),color:"#3b82f6",isDefault:!0};D([S])}})()},[n]),N.useEffect(()=>{if(!k)return;(async()=>{if(window.electronAPI)try{await window.electronAPI.saveSettings(l)}catch(I){console.error("Failed to save settings:",I)}})()},[l,k]),N.useEffect(()=>{l.imagefxAuth&&c.updateAuth(l.imagefxAuth)},[l.imagefxAuth,c]);const ke=async P=>{var T;if(!P.trim()){J("Empty Prompt","Please enter a description for your image");return}if(o){J("Generation in Progress","Please wait for the current generation to complete");return}if(l.aiService==="imagefx"&&(l.imagefxCount<1||l.imagefxCount>8)){A("Invalid Settings","Number of images must be between 1 and 8 for ImageFX");return}if(l.aiService==="imagefx"&&!l.imagefxAuth){A("Authentication Required","Please configure ImageFX authentication in settings");return}if(P.trim().length<3){J("Prompt Too Short","Please enter a more detailed description (at least 3 characters)");return}i(!0);const I=l.aiService==="imagefx"?l.imagefxCount:l.aiService==="gpt4free"&&l.gpt4freeCount||4,S=Array.from({length:I},(R,K)=>({id:`loading-${Date.now()}-${K}`,url:"",prompt:P,timestamp:Date.now(),service:l.aiService,isLoading:!0}));r(R=>[...S,...R]);try{if(l.aiService==="pollinations")await Ye(P,S);else if(l.aiService==="imagefx"){if(!((T=l.imagefxAuth)!=null&&T.trim())){A("ImageFX Authentication Required","Please enter your ImageFX authentication token in the settings"),r(R=>R.filter(K=>!S.some(te=>te.id===K.id)));return}if(l.imagefxAuth.length<20){A("Invalid ImageFX Token","The authentication token appears to be too short. Please check your token."),r(R=>R.filter(K=>!S.some(te=>te.id===K.id)));return}}else if(l.aiService==="gpt4free")await He(P,S);else throw new Error("Invalid AI service selected")}catch(R){console.error("Generation failed:",R),r(K=>K.filter(te=>!S.some(oe=>oe.id===te.id))),A("Generation Failed",R instanceof Error?R.message:"Unknown error occurred")}finally{i(!1)}},Ye=async(P,I)=>{const[S,T]=l.resolution.split("x").map(Number);let R=0;const K=I.map(async(te,oe)=>{try{const vt=l.pollinationsSeed!==void 0?l.pollinationsSeed+oe:Math.floor(Math.random()*1e6),ut=new URLSearchParams({width:S.toString(),height:T.toString(),seed:vt.toString(),model:l.pollinationsModel,...l.pollinationsNoLogo&&{nologo:"true"},...l.pollinationsEnhance&&{enhance:"true"},...l.pollinationsSafe&&{safe:"true"},...l.pollinationsPrivate&&{private:"true"}}),xe=`${window.electronAPI?"https://image.pollinations.ai":"/api/pollinations"}/prompt/${encodeURIComponent(P)}?${ut.toString()}`;await new Promise((Ve,Ce)=>{const rn=new Image;let Ko=!1;const Is=()=>{Ko||(Ko=!0,rn.onload=null,rn.onerror=null,rn.src="")},Yo=setTimeout(()=>{Is(),Ce(new Error("Image load timeout (30s)"))},3e4);rn.onload=()=>{clearTimeout(Yo),Is(),Ve()},rn.onerror=Ld=>{clearTimeout(Yo),Is(),console.error("Image load error for URL:",xe,Ld),Ce(new Error(`Failed to load image from ${xe}. This might be a CORS issue in development mode.`))},rn.crossOrigin="anonymous",rn.src=xe});const Dt={id:`pollinations-${Date.now()}-${oe}-${Math.random().toString(36).substring(2,11)}`,url:xe,prompt:P,timestamp:Date.now(),service:"pollinations"};if(window.electronAPI)try{const Ve=await window.electronAPI.saveGeneratedImage(xe,Dt);Ve.success&&(Dt.filename=Ve.filename)}catch(Ve){console.error("Failed to save generated image:",Ve)}r(Ve=>Ve.map(Ce=>Ce.id===te.id?Dt:Ce));const Qo=$.find(Ve=>Ve.isDefault);if(Qo&&!Qo.imageIds.includes(Dt.id)&&(D(Ve=>Ve.map(Ce=>Ce.isDefault&&!Ce.imageIds.includes(Dt.id)?{...Ce,imageIds:[...Ce.imageIds,Dt.id],updatedAt:Date.now()}:Ce)),window.electronAPI)){const Ve=$.map(Ce=>Ce.isDefault&&!Ce.imageIds.includes(Dt.id)?{...Ce,imageIds:[...Ce.imageIds,Dt.id],updatedAt:Date.now()}:Ce);window.electronAPI.saveCollections(Ve).catch(Ce=>{console.error("Failed to save collections after adding new image:",Ce)})}R++}catch(vt){console.error(`Failed to generate image ${oe}:`,vt),r(ut=>ut.filter(Fe=>Fe.id!==te.id))}});await Promise.allSettled(K),R>0?_("Images Generated",`Successfully generated ${R} image${R>1?"s":""}`):A("Generation Failed","No images could be generated. Please try again.")},He=async(P,I)=>{try{const[S,T]=l.resolution.split("x").map(Number),R=await xh.generateImages({prompt:P,model:l.gpt4freeModel||"flux",count:l.gpt4freeCount||4,width:S,height:T,seed:l.gpt4freeSeed});for(let te=0;te<R.length;te++){const oe=R[te],vt=I[te];if(vt){if(window.electronAPI)try{const Fe=await window.electronAPI.saveGeneratedImage(oe.url,oe);Fe.success&&(oe.filename=Fe.filename)}catch(Fe){console.error("Failed to save generated image:",Fe)}r(Fe=>Fe.map(xe=>xe.id===vt.id?oe:xe));const ut=$.find(Fe=>Fe.isDefault);if(ut&&!ut.imageIds.includes(oe.id)&&(D(Fe=>Fe.map(xe=>xe.isDefault&&!xe.imageIds.includes(oe.id)?{...xe,imageIds:[...xe.imageIds,oe.id],updatedAt:Date.now()}:xe)),window.electronAPI)){const Fe=$.map(xe=>xe.isDefault&&!xe.imageIds.includes(oe.id)?{...xe,imageIds:[...xe.imageIds,oe.id],updatedAt:Date.now()}:xe);window.electronAPI.saveCollections(Fe).catch(xe=>{console.error("Failed to save collections after adding new image:",xe)})}}}const K=I.slice(R.length);K.length>0&&r(te=>te.filter(oe=>!K.some(vt=>vt.id===oe.id))),R.length>0&&_("GPT4Free Generation Complete",`Successfully generated ${R.length} image${R.length>1?"s":""} with ${l.gpt4freeModel||"FLUX"}`)}catch(S){console.error("GPT4Free generation failed:",S),r(T=>T.filter(R=>!I.some(K=>K.id===R.id))),A("GPT4Free Generation Failed",S instanceof Error?S.message:"Unknown error occurred")}},be=P=>{x(P),g(!0)},vs=()=>{g(!1),x(null)},ws=()=>{w(P=>!P)},js=P=>{a(I=>({...I,prompt:P})),_("Prompt Applied","The selected prompt has been applied to the input field")},nr=P=>{d(P),p(!0)},Ns=()=>{p(!1),d(null)},nn=async(P,I,S)=>{if(!(!f||!window.electronAPI))try{const T={id:`edited-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,originalImageId:f.id,editedUrl:P,editType:I,editParams:S,timestamp:Date.now()},R=await window.electronAPI.saveGeneratedImage(P,{...f,id:T.id,url:P,timestamp:T.timestamp});R.success?(r(K=>K.map(te=>{if(te.id===f.id){const oe=te.editedVersions||[];return{...te,editedVersions:[...oe,{...T,filename:R.filename}]}}return te})),_("Edit Saved","Your edited image has been saved successfully")):A("Save Failed",R.error||"Failed to save edited image")}catch(T){console.error("Failed to save edited image:",T),A("Save Failed",T instanceof Error?T.message:"Unknown error occurred")}},ks=P=>{M(I=>{const S=new Set(I);return S.has(P)?S.delete(P):S.add(P),S})},bs=()=>{const P=new Set(n.filter(I=>!I.isLoading).map(I=>I.id));M(P)},kn=()=>{M(new Set)},Cs=()=>{E(P=>!P),F&&M(new Set)},Kr=async()=>{if(b.size===0){J("No Images Selected","Please select images to download");return}const P=n.filter(T=>b.has(T.id));let I=0,S=0;for(const T of P)try{await Jr(T),I++}catch(R){console.error(`Failed to download image ${T.id}:`,R),S++}I>0?_("Batch Download Complete",`Successfully downloaded ${I} image${I>1?"s":""}${S>0?`, ${S} failed`:""}`):A("Batch Download Failed","No images could be downloaded"),M(new Set),E(!1)},Ss=async()=>{if(b.size===0){J("No Images Selected","Please select images to delete");return}const P=n.filter(R=>b.has(R.id));let I=0,S=0;const T=[];for(const R of P)try{window.electronAPI&&R.filename?(await window.electronAPI.deleteGeneratedImage(R.filename)).success?(I++,T.push(R.id)):S++:R.filename?S++:(I++,T.push(R.id))}catch(K){console.error(`Failed to delete image ${R.id}:`,K),S++}if(T.length>0){r(K=>K.filter(te=>!T.includes(te.id)));const R=$.map(K=>({...K,imageIds:K.imageIds.filter(te=>!T.includes(te)),updatedAt:Date.now()}));if(D(R),window.electronAPI)try{await window.electronAPI.saveCollections(R)}catch(K){console.error("Failed to update collections after batch deletion:",K)}}I>0?_("Batch Delete Complete",`Successfully deleted ${I} image${I>1?"s":""}${S>0?`, ${S} failed`:""}`):A("Batch Delete Failed","No images could be deleted"),M(new Set),E(!1)},Es=async()=>{if(b.size===0){J("No Images Selected","Please select images to export");return}if(!window.electronAPI){A("Export Failed","Desktop functionality not available");return}try{const P=n.filter(T=>b.has(T.id)),I=P.map(T=>({id:T.id,url:T.url,prompt:T.prompt,timestamp:T.timestamp,service:T.service,filename:T.filename})),S=await window.electronAPI.exportImages(I);S.success?_("Export Complete",`Exported ${P.length} images to: ${S.path}`):A("Export Failed",S.error||"Unknown error occurred")}catch(P){console.error("Export failed:",P),A("Export Failed",P instanceof Error?P.message:"Unknown error occurred")}M(new Set),E(!1)},Yr=()=>{O(!0)},Zr=()=>{O(!1)},qr=()=>{Z(!0)},h=()=>{Z(!1)},v=async P=>{var I;try{if(!((I=P.name)!=null&&I.trim())){A("Invalid Collection Name","Collection name cannot be empty");return}if($.find(K=>K.name.toLowerCase()===P.name.trim().toLowerCase())){A("Duplicate Collection Name",`A collection named "${P.name}" already exists`);return}const T={...P,name:P.name.trim(),id:`collection-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,createdAt:Date.now(),updatedAt:Date.now()},R=[...$,T];if(D(R),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(R),_("Collection Created",`"${T.name}" has been created successfully`)}catch(K){console.error("Failed to save collections:",K),D($),A("Save Failed","Failed to save collection. Please try again.")}else _("Collection Created",`"${T.name}" has been created successfully`)}catch(S){console.error("Failed to create collection:",S),A("Collection Error","Failed to create collection. Please try again.")}},W=async(P,I)=>{var S;try{if(!$.find(K=>K.id===P)){A("Collection Error","Collection not found");return}if(I.name!==void 0){if(!((S=I.name)!=null&&S.trim())){A("Invalid Collection Name","Collection name cannot be empty");return}if($.find(te=>te.id!==P&&te.name.toLowerCase()===I.name.trim().toLowerCase())){A("Duplicate Collection Name",`A collection named "${I.name}" already exists`);return}}const R=$.map(K=>K.id===P?{...K,...I,...I.name&&{name:I.name.trim()},updatedAt:Date.now()}:K);if(D(R),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(R),_("Collection Updated","Collection has been updated successfully")}catch(K){console.error("Failed to save collections:",K),D($),A("Save Failed","Failed to save collection changes. Please try again.")}else _("Collection Updated","Collection has been updated successfully")}catch(T){console.error("Failed to update collection:",T),A("Collection Error","Failed to update collection. Please try again.")}},Y=async P=>{try{const I=$.find(T=>T.id===P);if(!I){A("Collection Error","Collection not found");return}if(I.isDefault){A("Cannot Delete",'The default "All Images" collection cannot be deleted');return}const S=$.filter(T=>T.id!==P);if(D(S),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(S),_("Collection Deleted",`"${I.name}" has been deleted`)}catch(T){console.error("Failed to save collections:",T),D($),A("Save Failed","Failed to delete collection. Please try again.")}else _("Collection Deleted",`"${I.name}" has been deleted`)}catch(I){console.error("Failed to delete collection:",I),A("Collection Error","Failed to delete collection. Please try again.")}},H=async(P,I)=>{try{const S=$.find(R=>R.id===P);if(!S){A("Collection Error","Collection not found");return}if(S.imageIds.includes(I)){J("Already Added",`Image is already in "${S.name}"`);return}const T=$.map(R=>R.id===P?{...R,imageIds:[...R.imageIds,I],updatedAt:Date.now()}:R);if(D(T),S.isDefault||_("Added to Collection",`Image added to "${S.name}"`),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(T)}catch(R){console.error("Failed to save collections:",R),D($),A("Save Failed","Failed to save collection changes")}}catch(S){console.error("Failed to add image to collection:",S),A("Collection Error","Failed to add image to collection")}},Q=async(P,I)=>{try{const S=$.map(T=>T.id===P?{...T,imageIds:T.imageIds.filter(R=>R!==I),updatedAt:Date.now()}:T);if(D(S),window.electronAPI&&typeof window.electronAPI.saveCollections=="function")try{await window.electronAPI.saveCollections(S)}catch(T){console.error("Failed to save collections:",T),D($),A("Save Failed","Failed to save collection changes")}}catch(S){console.error("Failed to remove image from collection:",S),A("Collection Error","Failed to remove image from collection")}},re=N.useMemo(()=>{let P=[...n];if(B.trim()){const I=B.toLowerCase();P=P.filter(S=>S.prompt.toLowerCase().includes(I)||S.service.toLowerCase().includes(I))}if(q!=="all"&&(P=P.filter(I=>I.service===q)),ie!=="all"){const I=new Date,S=new Date;switch(ie){case"today":S.setHours(0,0,0,0);break;case"week":S.setDate(I.getDate()-7);break;case"month":S.setMonth(I.getMonth()-1);break}P=P.filter(T=>new Date(T.timestamp)>=S)}return P.sort((I,S)=>{switch(G){case"newest":return S.timestamp-I.timestamp;case"oldest":return I.timestamp-S.timestamp;case"prompt":return I.prompt.localeCompare(S.prompt);default:return S.timestamp-I.timestamp}}),P},[n,B,q,ie,G]),Be=async()=>{if(window.electronAPI)try{const P=await window.electronAPI.refreshGeneratedImages();P.success&&P.images?(r(P.images),_("Images Refreshed","Image gallery has been updated")):A("Refresh Failed",P.error||"Unknown error occurred")}catch(P){console.error("Refresh failed:",P),A("Refresh Failed",P instanceof Error?P.message:"Unknown error occurred")}},bn=async P=>{if(!P){A("Delete Failed","Invalid image data");return}if(!window.electronAPI){A("Delete Failed","Desktop functionality not available");return}if(!P.filename){r(S=>S.filter(T=>T.id!==P.id));const I=$.map(S=>({...S,imageIds:S.imageIds.filter(T=>T!==P.id),updatedAt:Date.now()}));D(I),_("Image Removed","Image has been removed from the gallery");return}try{const I=await window.electronAPI.deleteGeneratedImage(P.filename);if(I.success){r(T=>T.filter(R=>R.id!==P.id));const S=$.map(T=>({...T,imageIds:T.imageIds.filter(R=>R!==P.id),updatedAt:Date.now()}));if(D(S),window.electronAPI)try{await window.electronAPI.saveCollections(S)}catch(T){console.error("Failed to update collections after image deletion:",T)}_("Image Deleted","Image has been deleted successfully")}else A("Delete Failed",I.error||"Unknown error occurred")}catch(I){console.error("Delete failed:",I),A("Delete Failed",I instanceof Error?I.message:"Unknown error occurred")}},Jr=async P=>{if(!window.electronAPI){A("Download Failed","Desktop functionality not available");return}try{let I;if(P.url.startsWith("data:image/"))I=P.url;else{const K=await fetch(P.url);if(!K.ok)throw new Error(`Failed to fetch image: ${K.statusText}`);const te=await K.blob();I=await new Promise((oe,vt)=>{const ut=new FileReader;ut.onload=()=>oe(ut.result),ut.onerror=()=>vt(new Error("Failed to read image data")),ut.readAsDataURL(te)})}const S=new Date().toISOString().replace(/[:.]/g,"-").slice(0,-5),T=`imagen-${P.service}-${S}.png`,R=await window.electronAPI.downloadImage(I,T);R.success?_("Image Downloaded",`Saved to: ${R.path}`):A("Download Failed",R.error||"Unknown error occurred")}catch(I){console.error("Download failed:",I),A("Download Failed",I instanceof Error?I.message:"Unknown error occurred")}};return s.jsx(mh,{children:s.jsxs("div",{className:"flex flex-col h-screen bg-app text-white relative",children:[s.jsx(fh,{currentView:e,onViewChange:t}),e==="generator"?s.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[s.jsx(th,{settings:l,onSettingsChange:a,onGenerate:ke,isGenerating:o}),s.jsx("div",{className:`flex-1 h-full transition-all duration-300 relative ${j?"lg:mr-96 mr-0":"mr-0"}`,children:s.jsx(sh,{images:re,totalImages:n.length,onDownload:Jr,onDelete:bn,onPreview:be,onRefresh:Be,onEdit:nr,selectedImages:b,isSelectionMode:F,onToggleSelection:ks,onToggleSelectionMode:Cs,onSelectAll:bs,onDeselectAll:kn,onBatchDownload:Kr,onBatchDelete:Ss,onBatchExport:Es,searchQuery:B,onSearchChange:L,filterService:q,onFilterServiceChange:ve,sortBy:G,onSortChange:ae,dateFilter:ie,onDateFilterChange:ge,onOpenComparison:Yr,onOpenCollections:qr,collections:$,onAddToCollection:H,onCreateCollection:v})})]}):s.jsx("div",{className:"flex-1 overflow-hidden",children:s.jsx(dh,{})}),e==="generator"&&s.jsxs(s.Fragment,{children:[s.jsx(oh,{isOpen:j,onToggle:ws,onPromptSelect:js}),s.jsx(ah,{image:u,isOpen:y,onClose:vs,onDownload:Jr,onDelete:bn,onEdit:nr}),s.jsx(ih,{image:f,isOpen:m,onClose:Ns,onSave:nn}),s.jsx(ch,{images:n,isOpen:ct,onClose:Zr,onDownload:Jr,onDelete:bn,onEdit:nr}),s.jsx(uh,{isOpen:ne,onClose:h,images:n,collections:$,onCreateCollection:v,onUpdateCollection:W,onDeleteCollection:Y,onAddImageToCollection:H,onRemoveImageFromCollection:Q,selectedImages:b})]}),s.jsx(hh,{toasts:we,onClose:C})]})})}ra.createRoot(document.getElementById("root")).render(s.jsx(nf.StrictMode,{children:s.jsx(vh,{})}));

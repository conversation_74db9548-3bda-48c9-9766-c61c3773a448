@echo off
echo Installing Python dependencies for GPT4Free Chat Service...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org/downloads/
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found. Installing dependencies...
echo.

REM Install core dependencies
pip install g4f>=0.2.0
if %errorlevel% neq 0 (
    echo WARNING: Failed to install g4f. Trying alternative installation...
    pip install --upgrade g4f
)

REM Install other dependencies
pip install aiohttp>=3.8.0 requests>=2.31.0 python-dotenv>=1.0.0

REM Install optional dependencies (ignore errors)
echo.
echo Installing optional dependencies...
pip install PyPDF2>=3.0.0 2>nul
pip install Pillow>=10.0.0 2>nul
pip install asyncio-throttle>=1.0.2 2>nul

echo.
echo Setup complete! You can now use the GPT4Free chat service.
echo.
pause

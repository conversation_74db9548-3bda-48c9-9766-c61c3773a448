import { spawn } from 'child_process';
import * as path from 'path';
import * as fs from 'fs';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
}

export interface ChatFile {
  name: string;
  type: string;
  data: string; // base64 encoded
}

export interface ChatRequest {
  model: string;
  provider: string;
  messages: ChatMessage[];
  files?: ChatFile[];
}

export interface ChatResponse {
  response: string;
  model: string;
  provider: string;
  timestamp: Date;
}

export class GPT4FreeChatService {
  private pythonPath: string;
  private scriptPath: string;
  private configured: boolean = false;

  constructor() {
    this.pythonPath = 'python';
    this.scriptPath = path.join(process.cwd(), 'python', 'gpt4free_chat.py');
    // Initialize configuration check asynchronously
    this.checkConfiguration().catch(error => {
      console.error('Failed to initialize GPT4Free configuration:', error);
      this.configured = false;
    });
  }

  private async checkConfiguration(): Promise<void> {
    try {
      // Check if Python script exists
      if (!fs.existsSync(this.scriptPath)) {
        console.warn('GPT4Free Python script not found:', this.scriptPath);
        this.configured = false;
        return;
      }

      // Test if Python and g4f are available
      try {
        const testResult = await this.executePythonScript(['-c', `
import sys
import json
import warnings
warnings.filterwarnings("ignore")
try:
    import g4f
    print(json.dumps({"status": "success", "message": "g4f library is available"}))
except ImportError as e:
    print(json.dumps({"status": "error", "message": f"g4f library not found: {str(e)}"}))
        `]);

        // Extract only the JSON part (ignore warnings)
        const lines = testResult.trim().split('\n');
        const jsonLine = lines.find(line => line.trim().startsWith('{'));

        if (jsonLine) {
          const result = JSON.parse(jsonLine);
          if (result.status === 'success') {
            this.configured = true;
            console.log('GPT4Free chat service configured successfully');
          } else {
            this.configured = false;
            console.warn('GPT4Free configuration failed:', result.message);
          }
        } else {
          this.configured = false;
          console.warn('Failed to parse GPT4Free configuration result');
        }
      } catch (error) {
        this.configured = false;
        console.warn('Failed to test GPT4Free configuration:', error);
      }
    } catch (error) {
      console.error('Error checking GPT4Free configuration:', error);
      this.configured = false;
    }
  }

  public isServiceConfigured(): boolean {
    return this.configured;
  }

  public async getAvailableModels(): Promise<string[]> {
    if (!this.configured) {
      return [
        'gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet',
        'gemini-pro', 'llama-2-70b', 'mixtral-8x7b', 'deepseek-coder'
      ];
    }

    try {
      // Create a simple Python script to get models
      const result = await this.executePythonScript(['-c', `
import sys
import json
import warnings
warnings.filterwarnings("ignore")
try:
    from python.gpt4free_chat import GPT4FreeChatHandler
    handler = GPT4FreeChatHandler()
    models = handler.get_available_models()
    print(json.dumps(models))
except Exception as e:
    print(json.dumps(['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet', 'gemini-pro', 'llama-2-70b', 'mixtral-8x7b', 'deepseek-coder']))
      `]);

      // Extract only the JSON part (ignore warnings)
      const lines = result.trim().split('\n');
      const jsonLine = lines.find(line => line.trim().startsWith('['));

      if (jsonLine) {
        return JSON.parse(jsonLine);
      } else {
        return ['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet', 'gemini-pro', 'llama-2-70b', 'mixtral-8x7b', 'deepseek-coder'];
      }
    } catch (error) {
      console.error('Error getting available models:', error);
      return ['gpt-4', 'gpt-3.5-turbo', 'claude-3-opus', 'claude-3-sonnet', 'gemini-pro', 'llama-2-70b', 'mixtral-8x7b', 'deepseek-coder'];
    }
  }

  public async getAvailableProviders(): Promise<string[]> {
    if (!this.configured) {
      return ['auto', 'bing', 'you', 'phind', 'liaobots', 'openai', 'chatgpt', 'gptgo', 'aichat', 'chatbase', 'freegpt', 'gptalk', 'koala', 'llama2', 'vercel'];
    }

    try {
      // Create a simple Python script to get providers
      const result = await this.executePythonScript(['-c', `
import sys
import json
import warnings
warnings.filterwarnings("ignore")
try:
    from python.gpt4free_chat import GPT4FreeChatHandler
    handler = GPT4FreeChatHandler()
    providers = handler.get_available_providers()
    print(json.dumps(providers))
except Exception as e:
    print(json.dumps(['auto', 'bing', 'you', 'phind', 'liaobots', 'openai', 'chatgpt', 'gptgo', 'aichat', 'chatbase', 'freegpt', 'gptalk', 'koala', 'llama2', 'vercel']))
      `]);

      // Extract only the JSON part (ignore warnings)
      const lines = result.trim().split('\n');
      const jsonLine = lines.find(line => line.trim().startsWith('['));

      if (jsonLine) {
        return JSON.parse(jsonLine);
      } else {
        return ['auto', 'bing', 'you', 'phind', 'liaobots', 'openai', 'chatgpt', 'gptgo', 'aichat', 'chatbase', 'freegpt', 'gptalk', 'koala', 'llama2', 'vercel'];
      }
    } catch (error) {
      console.error('Error getting available providers:', error);
      return ['auto', 'bing', 'you', 'phind', 'liaobots', 'openai', 'chatgpt', 'gptgo', 'aichat', 'chatbase', 'freegpt', 'gptalk', 'koala', 'llama2', 'vercel'];
    }
  }

  public async generateResponse(request: ChatRequest): Promise<ChatResponse> {
    if (!this.configured) {
      // Return mock response when not configured
      return {
        response: `Mock response for: "${request.messages[request.messages.length - 1]?.content}"\n\nThis is a demo response. To get real AI responses, please ensure Python and the g4f library are installed.`,
        model: request.model,
        provider: request.provider,
        timestamp: new Date()
      };
    }

    try {
      // Create temporary request file
      const tempDir = path.join(process.cwd(), 'temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      const requestFile = path.join(tempDir, `chat_request_${Date.now()}.json`);
      const requestData = {
        model: request.model,
        provider: request.provider,
        messages: request.messages,
        files: request.files || [],
        temperature: 0.7,
        max_tokens: 2000
      };

      fs.writeFileSync(requestFile, JSON.stringify(requestData, null, 2));

      try {
        // Execute Python script with request file
        const result = await this.executePythonScript([requestFile]);
        const response = JSON.parse(result);

        // Clean up temp file
        fs.unlinkSync(requestFile);

        if (response.error) {
          throw new Error(response.error);
        }

        return {
          response: response.response || 'No response received',
          model: request.model,
          provider: request.provider,
          timestamp: new Date()
        };
      } catch (error) {
        // Clean up temp file on error
        if (fs.existsSync(requestFile)) {
          fs.unlinkSync(requestFile);
        }
        throw error;
      }
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private executePythonScript(args: string[]): Promise<string> {
    return new Promise((resolve, reject) => {
      const pythonProcess = spawn(this.pythonPath, [this.scriptPath, ...args], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd()
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          resolve(stdout.trim());
        } else {
          reject(new Error(`Python script exited with code ${code}: ${stderr}`));
        }
      });

      pythonProcess.on('error', (error) => {
        reject(new Error(`Failed to spawn Python process: ${error.message}`));
      });

      // Set timeout to prevent hanging
      setTimeout(() => {
        pythonProcess.kill();
        reject(new Error('Python script execution timeout'));
      }, 60000); // 60 second timeout
    });
  }
}

// Export singleton instance
export const gpt4freeChatService = new GPT4FreeChatService();

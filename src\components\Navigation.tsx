import React from 'react';
import { Image, MessageCircle } from 'lucide-react';

interface NavigationProps {
  currentView: 'generator' | 'chatbot';
  onViewChange: (view: 'generator' | 'chatbot') => void;
}

const Navigation: React.FC<NavigationProps> = ({ currentView, onViewChange }) => {
  return (
    <div className="bg-gray-800 border-b border-gray-700 px-4 py-3">
      <div className="flex items-center gap-1">
        <button
          onClick={() => onViewChange('generator')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
            currentView === 'generator'
              ? 'bg-blue-600 text-white'
              : 'text-gray-300 hover:text-white hover:bg-gray-700'
          }`}
        >
          <Image className="w-4 h-4" />
          Image Generator
        </button>
        
        <button
          onClick={() => onViewChange('chatbot')}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors ${
            currentView === 'chatbot'
              ? 'bg-blue-600 text-white'
              : 'text-gray-300 hover:text-white hover:bg-gray-700'
          }`}
        >
          <MessageCircle className="w-4 h-4" />
          AI Chatbot
        </button>
      </div>
    </div>
  );
};

export default Navigation;

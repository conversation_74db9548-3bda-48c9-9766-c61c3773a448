import { GeneratedImage } from '../types'

export interface GPT4FreeOptions {
  prompt: string
  model?: 'flux' | 'dalle-3' | 'stable-diffusion-xl' | 'playground-v2.5' | 'ideogram'
  count?: number
  width?: number
  height?: number
  seed?: number
}

export interface GPT4FreeResult {
  id: string
  url: string
  prompt: string
  model: string
}

export class GPT4FreeService {
  private baseUrls = [
    'https://api.g4f.icu',
    'https://g4f.ai',
    'https://api.gpt4free.io'
  ]
  private currentUrlIndex = 0
  private configured = true // No auth required

  constructor() {}

  async generateImages(options: GPT4FreeOptions): Promise<GeneratedImage[]> {
    const {
      prompt,
      model = 'flux',
      count = 4,
      width = 1024,
      height = 1024,
      seed
    } = options

    try {
      const images: GeneratedImage[] = []
      
      // Generate multiple images
      for (let i = 0; i < count; i++) {
        const currentSeed = seed !== undefined ? seed + i : Math.floor(Math.random() * 1000000)
        
        const requestBody = {
          model: model,
          prompt: prompt,
          response_format: 'url',
          size: `${width}x${height}`,
          ...(seed !== undefined && { seed: currentSeed })
        }

        let response: Response | null = null
        let lastError: Error | null = null

        // Try multiple base URLs if one fails
        for (let urlIndex = 0; urlIndex < this.baseUrls.length; urlIndex++) {
          const baseUrl = this.baseUrls[(this.currentUrlIndex + urlIndex) % this.baseUrls.length]

          try {
            response = await fetch(`${baseUrl}/v1/images/generations`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              },
              body: JSON.stringify(requestBody)
            })

            if (response.ok) {
              // Update current URL index to the working one
              this.currentUrlIndex = (this.currentUrlIndex + urlIndex) % this.baseUrls.length
              break
            } else {
              lastError = new Error(`API error: ${response.status} ${response.statusText}`)
            }
          } catch (error) {
            lastError = error instanceof Error ? error : new Error('Network error')
            response = null
          }
        }

        if (!response || !response.ok) {
          // Try alternative endpoint if all main endpoints fail
          const altResponse = await this.generateWithAlternativeEndpoint(prompt, model, width, height, currentSeed)
          if (altResponse) {
            images.push(altResponse)
            continue
          }
          throw lastError || new Error('All GPT4Free endpoints failed')
        }

        const data = await response.json()
        
        if (data.data && data.data.length > 0) {
          const imageUrl = data.data[0].url
          
          const generatedImage: GeneratedImage = {
            id: `gpt4free-${Date.now()}-${i}-${Math.random().toString(36).substring(2, 11)}`,
            url: imageUrl,
            prompt,
            timestamp: Date.now(),
            service: 'gpt4free' as any, // We'll update the type later
          }
          
          images.push(generatedImage)
        }
      }

      if (images.length === 0) {
        throw new Error('No images were generated')
      }

      return images
    } catch (error) {
      console.error('GPT4Free generation error:', error)
      throw error instanceof Error ? error : new Error('Unknown error occurred during GPT4Free generation')
    }
  }

  private async generateWithAlternativeEndpoint(
    prompt: string,
    model: string,
    width: number,
    height: number,
    seed?: number
  ): Promise<GeneratedImage | null> {
    // Try alternative endpoints and fallback methods
    const alternativeEndpoints = [
      '/generate/image',
      '/api/generate',
      '/v1/generate'
    ]

    for (const baseUrl of this.baseUrls) {
      for (const endpoint of alternativeEndpoints) {
        try {
          const params = new URLSearchParams({
            prompt: prompt,
            model: model,
            width: width.toString(),
            height: height.toString(),
            ...(seed !== undefined && { seed: seed.toString() })
          })

          const response = await fetch(`${baseUrl}${endpoint}?${params.toString()}`, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
            }
          })

          if (response.ok) {
            const data = await response.json()

            if (data.url || data.image_url || data.data?.[0]?.url) {
              const imageUrl = data.url || data.image_url || data.data[0].url
              return {
                id: `gpt4free-alt-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
                url: imageUrl,
                prompt,
                timestamp: Date.now(),
                service: 'gpt4free' as any,
              }
            }
          }
        } catch (error) {
          // Continue to next endpoint
          continue
        }
      }
    }

    console.error('All alternative endpoints failed')
    return null
  }

  async testConnection(): Promise<boolean> {
    // Test connection to any available endpoint
    for (const baseUrl of this.baseUrls) {
      try {
        const response = await fetch(`${baseUrl}/v1/models`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          }
        })
        if (response.ok) {
          return true
        }
      } catch (error) {
        // Continue to next URL
        continue
      }
    }

    console.error('GPT4Free connection test failed for all endpoints')
    return false
  }

  isConfigured(): boolean {
    return this.configured
  }

  getAvailableModels(): string[] {
    return ['flux', 'dalle-3', 'stable-diffusion-xl', 'playground-v2.5', 'ideogram']
  }
}

// Create a singleton instance
export const gpt4freeService = new GPT4FreeService()

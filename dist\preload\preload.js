"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
electron_1.contextBridge.exposeInMainWorld('electronAPI', {
    downloadImage: (imageData, filename) => electron_1.ipcRenderer.invoke('download-image', imageData, filename),
    // Settings operations
    saveSettings: (settings) => electron_1.ipcRenderer.invoke('save-settings', settings),
    loadSettings: () => electron_1.ipcRenderer.invoke('load-settings'),
    // Export operations
    exportImages: (imageData) => electron_1.ipcRenderer.invoke('export-images', imageData),
    // Collections operations
    loadCollections: () => electron_1.ipcRenderer.invoke('load-collections'),
    saveCollections: (collections) => electron_1.ipcRenderer.invoke('save-collections', collections),
    // Generated images operations
    saveGeneratedImage: (imageData, imageInfo) => electron_1.ipcRenderer.invoke('save-generated-image', imageData, imageInfo),
    loadGeneratedImages: () => electron_1.ipcRenderer.invoke('load-generated-images'),
    deleteGeneratedImage: (filename) => electron_1.ipcRenderer.invoke('delete-generated-image', filename),
    // Refresh images manually
    refreshGeneratedImages: () => electron_1.ipcRenderer.invoke('refresh-generated-images'),
    // Listen for file system changes
    onImagesChanged: (callback) => {
        electron_1.ipcRenderer.on('images-changed', callback);
        return () => electron_1.ipcRenderer.removeListener('images-changed', callback);
    },
    // Prompt library operations
    savePrompts: (prompts) => electron_1.ipcRenderer.invoke('save-prompts', prompts),
    loadPrompts: () => electron_1.ipcRenderer.invoke('load-prompts'),
    // Qwen Chat operations
    openQwenChat: () => electron_1.ipcRenderer.invoke('open-qwen-chat'),
    clearQwenChatSession: () => electron_1.ipcRenderer.invoke('clear-qwen-chat-session'),
    // GPT4Free Chat operations
    gpt4freeChatGenerate: (request) => electron_1.ipcRenderer.invoke('gpt4free-chat-generate', request),
    gpt4freeChatGetModels: () => electron_1.ipcRenderer.invoke('gpt4free-chat-models'),
    gpt4freeChatGetProviders: () => electron_1.ipcRenderer.invoke('gpt4free-chat-providers'),
    gpt4freeChatIsConfigured: () => electron_1.ipcRenderer.invoke('gpt4free-chat-configured')
});

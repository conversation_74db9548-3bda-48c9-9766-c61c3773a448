#!/usr/bin/env python3
"""
GPT4Free Chat Service
A Python script that interfaces with the g4f library to provide AI chat capabilities.
"""

import sys
import json
import asyncio
import base64
import tempfile
import os
import warnings
from typing import List, Dict, Any, Optional

# Suppress warnings (especially pydub ffmpeg warnings)
warnings.filterwarnings("ignore")

try:
    import g4f
    from g4f.client import Client
    from g4f.Provider import (
        Bing, You, Phind, LiaoBots, OpenaiChat, 
        ChatgptAi, GptGo, Aichat, ChatBase, 
        FreeGpt, GPTalk, Koala, Llama2, Vercel
    )
    G4F_AVAILABLE = True
except ImportError:
    G4F_AVAILABLE = False
    print(json.dumps({
        "error": "g4f library not installed. Please install with: pip install g4f",
        "response": "GPT4Free library is not available. Please install it using 'pip install g4f'."
    }))
    sys.exit(1)

class GPT4FreeChatHandler:
    def __init__(self):
        self.client = Client()
        self.providers = {
            'auto': None,
            'bing': Bing,
            'you': You,
            'phind': Phind,
            'liaobots': <PERSON>oB<PERSON>,
            'openai': OpenaiChat,
            'chatgpt': ChatgptAi,
            'gptgo': GptGo,
            'aichat': Aichat,
            'chatbase': ChatBase,
            'freegpt': FreeGpt,
            'gptalk': GPTalk,
            'koala': Koala,
            'llama2': Llama2,
            'vercel': Vercel
        }
        
        self.models = {
            'gpt-4': 'gpt-4',
            'gpt-3.5-turbo': 'gpt-3.5-turbo',
            'claude-3-opus': 'claude-3-opus-20240229',
            'claude-3-sonnet': 'claude-3-sonnet-20240229',
            'gemini-pro': 'gemini-pro',
            'llama-2-70b': 'llama-2-70b-chat',
            'mixtral-8x7b': 'mixtral-8x7b-instruct-v0.1',
            'deepseek-coder': 'deepseek-coder-6.7b-instruct'
        }

    async def generate_response(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a response using g4f"""
        try:
            model = request_data.get('model', 'gpt-3.5-turbo')
            provider_name = request_data.get('provider', 'auto')
            messages = request_data.get('messages', [])
            files = request_data.get('files', [])
            temperature = request_data.get('temperature', 0.7)
            max_tokens = request_data.get('max_tokens', 2000)
            
            # Map model name
            g4f_model = self.models.get(model, model)
            
            # Get provider
            provider = self.providers.get(provider_name.lower()) if provider_name != 'auto' else None
            
            # Process files if any
            processed_messages = await self.process_messages_with_files(messages, files)
            
            # Generate response
            if provider:
                response = await self.client.chat.completions.create(
                    model=g4f_model,
                    messages=processed_messages,
                    provider=provider,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
            else:
                # Auto-select provider
                response = await self.client.chat.completions.create(
                    model=g4f_model,
                    messages=processed_messages,
                    temperature=temperature,
                    max_tokens=max_tokens
                )
            
            return {
                'response': response.choices[0].message.content,
                'model': g4f_model,
                'provider': provider.__name__ if provider else 'auto',
                'usage': {
                    'prompt_tokens': getattr(response.usage, 'prompt_tokens', 0) if hasattr(response, 'usage') else 0,
                    'completion_tokens': getattr(response.usage, 'completion_tokens', 0) if hasattr(response, 'usage') else 0,
                    'total_tokens': getattr(response.usage, 'total_tokens', 0) if hasattr(response, 'usage') else 0
                }
            }
            
        except Exception as e:
            # Fallback to simple g4f.ChatCompletion if client fails
            try:
                response_text = await g4f.ChatCompletion.create_async(
                    model=g4f_model,
                    messages=processed_messages,
                    provider=provider
                )
                
                return {
                    'response': response_text,
                    'model': g4f_model,
                    'provider': provider.__name__ if provider else 'auto',
                    'usage': {
                        'prompt_tokens': 0,
                        'completion_tokens': 0,
                        'total_tokens': 0
                    }
                }
            except Exception as fallback_error:
                return {
                    'response': f"I apologize, but I encountered an error while processing your request. Error details: {str(e)}. Fallback error: {str(fallback_error)}",
                    'model': g4f_model,
                    'provider': 'error',
                    'usage': {
                        'prompt_tokens': 0,
                        'completion_tokens': 0,
                        'total_tokens': 0
                    }
                }

    async def process_messages_with_files(self, messages: List[Dict], files: List[Dict]) -> List[Dict]:
        """Process messages and incorporate file content"""
        processed_messages = messages.copy()
        
        if files:
            file_contents = []
            for file in files:
                try:
                    file_content = await self.process_file(file)
                    if file_content:
                        file_contents.append(file_content)
                except Exception as e:
                    file_contents.append(f"Error processing file {file.get('name', 'unknown')}: {str(e)}")
            
            if file_contents:
                # Add file content to the last user message
                if processed_messages and processed_messages[-1]['role'] == 'user':
                    processed_messages[-1]['content'] += '\n\nAttached files:\n' + '\n\n'.join(file_contents)
                else:
                    processed_messages.append({
                        'role': 'user',
                        'content': 'Attached files:\n' + '\n\n'.join(file_contents)
                    })
        
        return processed_messages

    async def process_file(self, file: Dict) -> Optional[str]:
        """Process individual file based on its type"""
        try:
            file_name = file.get('name', 'unknown')
            file_type = file.get('type', '')
            file_data = file.get('data', '')
            
            if not file_data:
                return f"[File: {file_name}] - No data provided"
            
            # Decode base64 data
            if ',' in file_data:
                file_data = file_data.split(',', 1)[1]
            
            decoded_data = base64.b64decode(file_data)
            
            if file_type.startswith('text/') or file_type in ['application/json', 'application/xml']:
                # Text files
                try:
                    content = decoded_data.decode('utf-8')
                    return f"[File: {file_name}]\nContent:\n{content}"
                except UnicodeDecodeError:
                    return f"[File: {file_name}] - Unable to decode as text"
            
            elif file_type.startswith('image/'):
                # Images - for now just indicate it's an image
                # In a full implementation, you might use OCR or image analysis
                return f"[Image: {file_name}] - Image file uploaded (type: {file_type}). Please describe what you'd like me to analyze about this image."
            
            elif file_type == 'application/pdf':
                # PDF files - would need PyPDF2 or similar
                return f"[PDF: {file_name}] - PDF file uploaded. PDF text extraction not implemented in this basic version."
            
            elif file_type.startswith('audio/'):
                return f"[Audio: {file_name}] - Audio file uploaded (type: {file_type}). Audio processing not implemented."
            
            elif file_type.startswith('video/'):
                return f"[Video: {file_name}] - Video file uploaded (type: {file_type}). Video processing not implemented."
            
            else:
                return f"[File: {file_name}] - File uploaded (type: {file_type}, size: {len(decoded_data)} bytes). Binary file processing not implemented."
                
        except Exception as e:
            return f"[File: {file.get('name', 'unknown')}] - Error processing file: {str(e)}"

    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        return list(self.models.keys())

    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        return list(self.providers.keys())

async def main():
    """Main function to handle the chat request"""
    if len(sys.argv) != 2:
        print(json.dumps({
            "error": "Usage: python gpt4free_chat.py <request_file>",
            "response": "Invalid usage. Please provide a request file."
        }))
        sys.exit(1)
    
    request_file = sys.argv[1]
    
    try:
        # Read request data
        with open(request_file, 'r', encoding='utf-8') as f:
            request_data = json.load(f)
        
        # Create handler and generate response
        handler = GPT4FreeChatHandler()
        response = await handler.generate_response(request_data)
        
        # Output response as JSON
        print(json.dumps(response, ensure_ascii=False, indent=2))
        
    except FileNotFoundError:
        print(json.dumps({
            "error": f"Request file not found: {request_file}",
            "response": "The request file could not be found."
        }))
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(json.dumps({
            "error": f"Invalid JSON in request file: {str(e)}",
            "response": "The request file contains invalid JSON."
        }))
        sys.exit(1)
    except Exception as e:
        print(json.dumps({
            "error": f"Unexpected error: {str(e)}",
            "response": f"An unexpected error occurred: {str(e)}"
        }))
        sys.exit(1)

if __name__ == "__main__":
    if sys.platform == 'win32':
        # Windows specific event loop policy
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main())
